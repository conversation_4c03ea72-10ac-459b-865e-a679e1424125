-- =====================================================
-- COMPLETE DYNAMIC ENUM SYSTEM SETUP
-- =====================================================
-- This script sets up the complete dynamic enum system for Hospital Management
-- Run this script in Supabase SQL Editor to deploy the entire enum system
--
-- IMPORTANT: This script is safe to run multiple times (uses IF NOT EXISTS)
--
-- What this script does:
-- 1. Creates enum tables (enum_categories, system_enums)
-- 2. Sets up RLS policies
-- 3. Creates all helper functions
-- 4. Populates all enum data
-- 5. Creates validation triggers
-- 6. Creates views for easy access
-- 7. Verifies the setup

-- =====================================================
-- STEP 1: CREATE ENUM TABLES
-- =====================================================

-- Create enum_categories table
CREATE TABLE IF NOT EXISTS enum_categories (
  category_id TEXT PRIMARY KEY,
  category_name TEXT UNIQUE NOT NULL,
  display_name_en TEXT NOT NULL,
  display_name_vi TEXT NOT NULL,
  description TEXT,
  is_system BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create system_enums table
CREATE TABLE IF NOT EXISTS system_enums (
  enum_id TEXT PRIMARY KEY DEFAULT ('ENUM' || EXTRACT(EPOCH FROM NOW())::BIGINT),
  category_id TEXT NOT NULL REFERENCES enum_categories(category_id) ON DELETE CASCADE,
  enum_key TEXT NOT NULL,
  display_name_en TEXT NOT NULL,
  display_name_vi TEXT NOT NULL,
  description_en TEXT,
  description_vi TEXT,
  sort_order INTEGER DEFAULT 0,
  color_code TEXT,
  icon_name TEXT,
  is_default BOOLEAN DEFAULT false,
  is_system BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(category_id, enum_key)
);

-- =====================================================
-- STEP 2: ENABLE RLS AND CREATE POLICIES
-- =====================================================

-- Enable RLS
ALTER TABLE enum_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_enums ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Everyone can view active enum categories" ON enum_categories;
DROP POLICY IF EXISTS "Admins can manage enum categories" ON enum_categories;
DROP POLICY IF EXISTS "Everyone can view active enums" ON system_enums;
DROP POLICY IF EXISTS "Admins can manage enums" ON system_enums;

-- Create RLS policies
CREATE POLICY "Everyone can view active enum categories" ON enum_categories
  FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage enum categories" ON enum_categories
  FOR ALL USING (
    EXISTS (SELECT 1 FROM profiles p WHERE p.id = auth.uid() AND p.role = 'admin')
  );

CREATE POLICY "Everyone can view active enums" ON system_enums
  FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage enums" ON system_enums
  FOR ALL USING (
    EXISTS (SELECT 1 FROM profiles p WHERE p.id = auth.uid() AND p.role = 'admin')
  );

-- =====================================================
-- STEP 3: CREATE INDEXES
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_system_enums_category ON system_enums(category_id);
CREATE INDEX IF NOT EXISTS idx_system_enums_key ON system_enums(enum_key);
CREATE INDEX IF NOT EXISTS idx_system_enums_active ON system_enums(is_active);
CREATE INDEX IF NOT EXISTS idx_system_enums_sort ON system_enums(category_id, sort_order);
CREATE INDEX IF NOT EXISTS idx_enum_categories_name ON enum_categories(category_name);
CREATE INDEX IF NOT EXISTS idx_enum_categories_active ON enum_categories(is_active);

-- =====================================================
-- STEP 4: CREATE HELPER FUNCTIONS
-- =====================================================

-- Function to get enum display name by language
CREATE OR REPLACE FUNCTION get_enum_display_name(
  p_category_id TEXT,
  p_enum_key TEXT,
  p_language TEXT DEFAULT 'vi'
)
RETURNS TEXT AS $$
DECLARE
  result TEXT;
BEGIN
  IF p_language = 'en' THEN
    SELECT display_name_en INTO result
    FROM system_enums
    WHERE category_id = p_category_id
      AND enum_key = p_enum_key
      AND is_active = true;
  ELSE
    SELECT display_name_vi INTO result
    FROM system_enums
    WHERE category_id = p_category_id
      AND enum_key = p_enum_key
      AND is_active = true;
  END IF;

  RETURN COALESCE(result, p_enum_key);
END;
$$ LANGUAGE plpgsql;

-- Function to validate enum value
CREATE OR REPLACE FUNCTION validate_enum_value(
  p_category_id TEXT,
  p_enum_key TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM system_enums
    WHERE category_id = p_category_id
      AND enum_key = p_enum_key
      AND is_active = true
  );
END;
$$ LANGUAGE plpgsql;

-- Function to get default enum value for a category
CREATE OR REPLACE FUNCTION get_default_enum_value(p_category_id TEXT)
RETURNS TEXT AS $$
DECLARE
  result TEXT;
BEGIN
  SELECT enum_key INTO result
  FROM system_enums
  WHERE category_id = p_category_id
    AND is_default = true
    AND is_active = true
  LIMIT 1;

  IF result IS NULL THEN
    SELECT enum_key INTO result
    FROM system_enums
    WHERE category_id = p_category_id
      AND is_active = true
    ORDER BY sort_order, enum_key
    LIMIT 1;
  END IF;

  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Function to get all enums for a category
CREATE OR REPLACE FUNCTION get_enums_by_category(
  p_category_id TEXT,
  p_language TEXT DEFAULT 'vi'
)
RETURNS TABLE(
  enum_key TEXT,
  display_name TEXT,
  description TEXT,
  sort_order INTEGER,
  color_code TEXT,
  icon_name TEXT,
  is_default BOOLEAN
) AS $$
BEGIN
  IF p_language = 'en' THEN
    RETURN QUERY
    SELECT
      se.enum_key,
      se.display_name_en as display_name,
      se.description_en as description,
      se.sort_order,
      se.color_code,
      se.icon_name,
      se.is_default
    FROM system_enums se
    WHERE se.category_id = p_category_id
      AND se.is_active = true
    ORDER BY se.sort_order, se.display_name_en;
  ELSE
    RETURN QUERY
    SELECT
      se.enum_key,
      se.display_name_vi as display_name,
      se.description_vi as description,
      se.sort_order,
      se.color_code,
      se.icon_name,
      se.is_default
    FROM system_enums se
    WHERE se.category_id = p_category_id
      AND se.is_active = true
    ORDER BY se.sort_order, se.display_name_vi;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Trigger function for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add triggers for enum tables
DROP TRIGGER IF EXISTS update_enum_categories_updated_at ON enum_categories;
CREATE TRIGGER update_enum_categories_updated_at
  BEFORE UPDATE ON enum_categories
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_system_enums_updated_at ON system_enums;
CREATE TRIGGER update_system_enums_updated_at
  BEFORE UPDATE ON system_enums
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- STEP 5: POPULATE ENUM CATEGORIES
-- =====================================================

INSERT INTO enum_categories (category_id, category_name, display_name_en, display_name_vi, description, is_system) VALUES
('ROLE', 'user_role', 'User Role', 'Vai trò người dùng', 'System user roles', true),
('GENDER', 'gender', 'Gender', 'Giới tính', 'Gender options', true),
('BLOOD_TYPE', 'blood_type', 'Blood Type', 'Nhóm máu', 'Blood type classifications', true),
('DOCTOR_STATUS', 'doctor_status', 'Doctor Status', 'Trạng thái bác sĩ', 'Doctor working status', true),
('PATIENT_STATUS', 'patient_status', 'Patient Status', 'Trạng thái bệnh nhân', 'Patient status in system', true),
('APPOINTMENT_TYPE', 'appointment_type', 'Appointment Type', 'Loại cuộc hẹn', 'Types of medical appointments', true),
('APPOINTMENT_STATUS', 'appointment_status', 'Appointment Status', 'Trạng thái cuộc hẹn', 'Status of appointments', true),
('ROOM_TYPE', 'room_type', 'Room Type', 'Loại phòng', 'Types of hospital rooms', true),
('ROOM_STATUS', 'room_status', 'Room Status', 'Trạng thái phòng', 'Room availability status', true),
('MEDICAL_RECORD_STATUS', 'medical_record_status', 'Medical Record Status', 'Trạng thái hồ sơ y tế', 'Status of medical records', true),
('PRESCRIPTION_STATUS', 'prescription_status', 'Prescription Status', 'Trạng thái đơn thuốc', 'Status of prescriptions', true)
ON CONFLICT (category_id) DO NOTHING;

-- =====================================================
-- STEP 6: POPULATE ENUM VALUES
-- =====================================================

-- User Roles
INSERT INTO system_enums (category_id, enum_key, display_name_en, display_name_vi, description_en, description_vi, sort_order, color_code, icon_name, is_default, is_system) VALUES
('ROLE', 'admin', 'Administrator', 'Quản trị viên', 'System administrator', 'Quản trị viên hệ thống', 1, '#dc3545', 'shield-check', false, true),
('ROLE', 'doctor', 'Doctor', 'Bác sĩ', 'Medical doctor', 'Bác sĩ y khoa', 2, '#007bff', 'stethoscope', false, true),
('ROLE', 'patient', 'Patient', 'Bệnh nhân', 'Hospital patient', 'Bệnh nhân bệnh viện', 3, '#28a745', 'person', true, true)
ON CONFLICT (category_id, enum_key) DO NOTHING;

-- Gender
INSERT INTO system_enums (category_id, enum_key, display_name_en, display_name_vi, description_en, description_vi, sort_order, color_code, icon_name, is_system) VALUES
('GENDER', 'male', 'Male', 'Nam', 'Male gender', 'Giới tính nam', 1, '#007bff', 'person-standing', true),
('GENDER', 'female', 'Female', 'Nữ', 'Female gender', 'Giới tính nữ', 2, '#e83e8c', 'person-dress', true),
('GENDER', 'other', 'Other', 'Khác', 'Other gender', 'Giới tính khác', 3, '#6c757d', 'person', true)
ON CONFLICT (category_id, enum_key) DO NOTHING;

-- Blood Types
INSERT INTO system_enums (category_id, enum_key, display_name_en, display_name_vi, sort_order, color_code, is_system) VALUES
('BLOOD_TYPE', 'A+', 'A+', 'A+', 1, '#dc3545', true),
('BLOOD_TYPE', 'A-', 'A-', 'A-', 2, '#dc3545', true),
('BLOOD_TYPE', 'B+', 'B+', 'B+', 3, '#fd7e14', true),
('BLOOD_TYPE', 'B-', 'B-', 'B-', 4, '#fd7e14', true),
('BLOOD_TYPE', 'AB+', 'AB+', 'AB+', 5, '#6f42c1', true),
('BLOOD_TYPE', 'AB-', 'AB-', 'AB-', 6, '#6f42c1', true),
('BLOOD_TYPE', 'O+', 'O+', 'O+', 7, '#28a745', true),
('BLOOD_TYPE', 'O-', 'O-', 'O-', 8, '#28a745', true)
ON CONFLICT (category_id, enum_key) DO NOTHING;

-- Doctor Status
INSERT INTO system_enums (category_id, enum_key, display_name_en, display_name_vi, description_en, description_vi, sort_order, color_code, icon_name, is_default, is_system) VALUES
('DOCTOR_STATUS', 'active', 'Active', 'Đang làm việc', 'Doctor is actively working', 'Bác sĩ đang làm việc', 1, '#28a745', 'check-circle', true, true),
('DOCTOR_STATUS', 'inactive', 'Inactive', 'Không hoạt động', 'Doctor is not working', 'Bác sĩ không làm việc', 2, '#6c757d', 'x-circle', false, true),
('DOCTOR_STATUS', 'on_leave', 'On Leave', 'Đang nghỉ phép', 'Doctor is on leave', 'Bác sĩ đang nghỉ phép', 3, '#ffc107', 'clock', false, true)
ON CONFLICT (category_id, enum_key) DO NOTHING;

-- Patient Status
INSERT INTO system_enums (category_id, enum_key, display_name_en, display_name_vi, description_en, description_vi, sort_order, color_code, icon_name, is_default, is_system) VALUES
('PATIENT_STATUS', 'active', 'Active', 'Đang điều trị', 'Patient is receiving care', 'Bệnh nhân đang được điều trị', 1, '#28a745', 'heart-pulse', true, true),
('PATIENT_STATUS', 'inactive', 'Inactive', 'Không hoạt động', 'Patient is not active', 'Bệnh nhân không hoạt động', 2, '#6c757d', 'pause-circle', false, true),
('PATIENT_STATUS', 'deceased', 'Deceased', 'Đã qua đời', 'Patient has passed away', 'Bệnh nhân đã qua đời', 3, '#000000', 'x-circle', false, true)
ON CONFLICT (category_id, enum_key) DO NOTHING;

-- Appointment Types
INSERT INTO system_enums (category_id, enum_key, display_name_en, display_name_vi, description_en, description_vi, sort_order, color_code, icon_name, is_default, is_system) VALUES
('APPOINTMENT_TYPE', 'consultation', 'Consultation', 'Khám bệnh', 'Regular consultation', 'Khám bệnh thường quy', 1, '#007bff', 'chat-dots', true, true),
('APPOINTMENT_TYPE', 'follow_up', 'Follow-up', 'Tái khám', 'Follow-up appointment', 'Cuộc hẹn tái khám', 2, '#17a2b8', 'arrow-repeat', false, true),
('APPOINTMENT_TYPE', 'emergency', 'Emergency', 'Cấp cứu', 'Emergency appointment', 'Cuộc hẹn cấp cứu', 3, '#dc3545', 'exclamation-triangle', false, true),
('APPOINTMENT_TYPE', 'surgery', 'Surgery', 'Phẫu thuật', 'Surgical procedure', 'Thủ thuật phẫu thuật', 4, '#6f42c1', 'scissors', false, true)
ON CONFLICT (category_id, enum_key) DO NOTHING;

-- Appointment Status
INSERT INTO system_enums (category_id, enum_key, display_name_en, display_name_vi, description_en, description_vi, sort_order, color_code, icon_name, is_default, is_system) VALUES
('APPOINTMENT_STATUS', 'scheduled', 'Scheduled', 'Đã lên lịch', 'Appointment is scheduled', 'Cuộc hẹn đã được lên lịch', 1, '#ffc107', 'calendar-plus', true, true),
('APPOINTMENT_STATUS', 'confirmed', 'Confirmed', 'Đã xác nhận', 'Appointment is confirmed', 'Cuộc hẹn đã được xác nhận', 2, '#007bff', 'calendar-check', false, true),
('APPOINTMENT_STATUS', 'in_progress', 'In Progress', 'Đang diễn ra', 'Appointment in progress', 'Cuộc hẹn đang diễn ra', 3, '#17a2b8', 'clock', false, true),
('APPOINTMENT_STATUS', 'completed', 'Completed', 'Hoàn thành', 'Appointment completed', 'Cuộc hẹn đã hoàn thành', 4, '#28a745', 'check-circle', false, true),
('APPOINTMENT_STATUS', 'cancelled', 'Cancelled', 'Đã hủy', 'Appointment cancelled', 'Cuộc hẹn đã bị hủy', 5, '#dc3545', 'x-circle', false, true),
('APPOINTMENT_STATUS', 'no_show', 'No Show', 'Không đến', 'Patient did not show up', 'Bệnh nhân không đến', 6, '#6c757d', 'person-x', false, true)
ON CONFLICT (category_id, enum_key) DO NOTHING;

-- Room Types
INSERT INTO system_enums (category_id, enum_key, display_name_en, display_name_vi, description_en, description_vi, sort_order, color_code, icon_name, is_system) VALUES
('ROOM_TYPE', 'consultation', 'Consultation Room', 'Phòng khám', 'Room for consultations', 'Phòng dành cho khám bệnh', 1, '#007bff', 'chat-dots', true),
('ROOM_TYPE', 'surgery', 'Surgery Room', 'Phòng mổ', 'Operating room', 'Phòng mổ cho phẫu thuật', 2, '#dc3545', 'scissors', true),
('ROOM_TYPE', 'emergency', 'Emergency Room', 'Phòng cấp cứu', 'Emergency treatment room', 'Phòng điều trị cấp cứu', 3, '#ffc107', 'exclamation-triangle', true),
('ROOM_TYPE', 'ward', 'Ward', 'Phòng bệnh', 'Patient ward room', 'Phòng bệnh nhân', 4, '#28a745', 'house', true),
('ROOM_TYPE', 'icu', 'ICU', 'Phòng hồi sức', 'Intensive Care Unit', 'Phòng chăm sóc đặc biệt', 5, '#6f42c1', 'heart-pulse', true),
('ROOM_TYPE', 'laboratory', 'Laboratory', 'Phòng xét nghiệm', 'Medical laboratory', 'Phòng xét nghiệm y tế', 6, '#17a2b8', 'flask', true)
ON CONFLICT (category_id, enum_key) DO NOTHING;

-- Room Status
INSERT INTO system_enums (category_id, enum_key, display_name_en, display_name_vi, description_en, description_vi, sort_order, color_code, icon_name, is_default, is_system) VALUES
('ROOM_STATUS', 'available', 'Available', 'Trống', 'Room is available', 'Phòng có thể sử dụng', 1, '#28a745', 'check-circle', true, true),
('ROOM_STATUS', 'occupied', 'Occupied', 'Đang sử dụng', 'Room is occupied', 'Phòng đang được sử dụng', 2, '#dc3545', 'person-fill', false, true),
('ROOM_STATUS', 'maintenance', 'Maintenance', 'Bảo trì', 'Room under maintenance', 'Phòng đang bảo trì', 3, '#ffc107', 'tools', false, true),
('ROOM_STATUS', 'out_of_service', 'Out of Service', 'Ngừng hoạt động', 'Room out of service', 'Phòng ngừng hoạt động', 4, '#6c757d', 'x-circle', false, true)
ON CONFLICT (category_id, enum_key) DO NOTHING;

-- Medical Record Status
INSERT INTO system_enums (category_id, enum_key, display_name_en, display_name_vi, description_en, description_vi, sort_order, color_code, icon_name, is_default, is_system) VALUES
('MEDICAL_RECORD_STATUS', 'active', 'Active', 'Đang hoạt động', 'Record is active', 'Hồ sơ đang hoạt động', 1, '#28a745', 'file-medical', true, true),
('MEDICAL_RECORD_STATUS', 'archived', 'Archived', 'Đã lưu trữ', 'Record is archived', 'Hồ sơ đã được lưu trữ', 2, '#6c757d', 'archive', false, true)
ON CONFLICT (category_id, enum_key) DO NOTHING;

-- Prescription Status
INSERT INTO system_enums (category_id, enum_key, display_name_en, display_name_vi, description_en, description_vi, sort_order, color_code, icon_name, is_default, is_system) VALUES
('PRESCRIPTION_STATUS', 'active', 'Active', 'Đang hiệu lực', 'Prescription is active', 'Đơn thuốc đang hiệu lực', 1, '#28a745', 'prescription', true, true),
('PRESCRIPTION_STATUS', 'dispensed', 'Dispensed', 'Đã cấp phát', 'Prescription dispensed', 'Đơn thuốc đã được cấp phát', 2, '#007bff', 'check-circle', false, true),
('PRESCRIPTION_STATUS', 'expired', 'Expired', 'Đã hết hạn', 'Prescription expired', 'Đơn thuốc đã hết hạn', 3, '#ffc107', 'clock', false, true),
('PRESCRIPTION_STATUS', 'cancelled', 'Cancelled', 'Đã hủy', 'Prescription cancelled', 'Đơn thuốc đã bị hủy', 4, '#dc3545', 'x-circle', false, true)
ON CONFLICT (category_id, enum_key) DO NOTHING;

-- =====================================================
-- STEP 7: CREATE VALIDATION FUNCTIONS FOR TRIGGERS
-- =====================================================

-- Function to validate profile role
CREATE OR REPLACE FUNCTION validate_profile_role()
RETURNS TRIGGER AS $$
BEGIN
  IF NOT validate_enum_value('ROLE', NEW.role) THEN
    RAISE EXCEPTION 'Invalid role: %. Must be a valid enum value from ROLE category.', NEW.role;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to validate doctor status
CREATE OR REPLACE FUNCTION validate_doctor_status()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.status IS NOT NULL AND NOT validate_enum_value('DOCTOR_STATUS', NEW.status) THEN
    RAISE EXCEPTION 'Invalid doctor status: %. Must be a valid enum value from DOCTOR_STATUS category.', NEW.status;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to validate patient data
CREATE OR REPLACE FUNCTION validate_patient_data()
RETURNS TRIGGER AS $$
BEGIN
  -- Validate gender
  IF NEW.gender IS NOT NULL AND NOT validate_enum_value('GENDER', NEW.gender) THEN
    RAISE EXCEPTION 'Invalid gender: %. Must be a valid enum value from GENDER category.', NEW.gender;
  END IF;

  -- Validate blood type
  IF NEW.blood_type IS NOT NULL AND NOT validate_enum_value('BLOOD_TYPE', NEW.blood_type) THEN
    RAISE EXCEPTION 'Invalid blood type: %. Must be a valid enum value from BLOOD_TYPE category.', NEW.blood_type;
  END IF;

  -- Validate status
  IF NEW.status IS NOT NULL AND NOT validate_enum_value('PATIENT_STATUS', NEW.status) THEN
    RAISE EXCEPTION 'Invalid patient status: %. Must be a valid enum value from PATIENT_STATUS category.', NEW.status;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to validate appointment data
CREATE OR REPLACE FUNCTION validate_appointment_data()
RETURNS TRIGGER AS $$
BEGIN
  -- Validate type
  IF NEW.type IS NOT NULL AND NOT validate_enum_value('APPOINTMENT_TYPE', NEW.type) THEN
    RAISE EXCEPTION 'Invalid appointment type: %. Must be a valid enum value from APPOINTMENT_TYPE category.', NEW.type;
  END IF;

  -- Validate status
  IF NEW.status IS NOT NULL AND NOT validate_enum_value('APPOINTMENT_STATUS', NEW.status) THEN
    RAISE EXCEPTION 'Invalid appointment status: %. Must be a valid enum value from APPOINTMENT_STATUS category.', NEW.status;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to validate room data
CREATE OR REPLACE FUNCTION validate_room_data()
RETURNS TRIGGER AS $$
BEGIN
  -- Validate room type
  IF NEW.room_type IS NOT NULL AND NOT validate_enum_value('ROOM_TYPE', NEW.room_type) THEN
    RAISE EXCEPTION 'Invalid room type: %. Must be a valid enum value from ROOM_TYPE category.', NEW.room_type;
  END IF;

  -- Validate status
  IF NEW.status IS NOT NULL AND NOT validate_enum_value('ROOM_STATUS', NEW.status) THEN
    RAISE EXCEPTION 'Invalid room status: %. Must be a valid enum value from ROOM_STATUS category.', NEW.status;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to validate medical record status
CREATE OR REPLACE FUNCTION validate_medical_record_status()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.status IS NOT NULL AND NOT validate_enum_value('MEDICAL_RECORD_STATUS', NEW.status) THEN
    RAISE EXCEPTION 'Invalid medical record status: %. Must be a valid enum value from MEDICAL_RECORD_STATUS category.', NEW.status;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to validate prescription status
CREATE OR REPLACE FUNCTION validate_prescription_status()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.status IS NOT NULL AND NOT validate_enum_value('PRESCRIPTION_STATUS', NEW.status) THEN
    RAISE EXCEPTION 'Invalid prescription status: %. Must be a valid enum value from PRESCRIPTION_STATUS category.', NEW.status;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- STEP 8: CREATE VALIDATION TRIGGERS
-- =====================================================

-- Profiles table triggers
DROP TRIGGER IF EXISTS trigger_validate_profile_role ON profiles;
CREATE TRIGGER trigger_validate_profile_role
  BEFORE INSERT OR UPDATE ON profiles
  FOR EACH ROW EXECUTE FUNCTION validate_profile_role();

-- Doctors table triggers
DROP TRIGGER IF EXISTS trigger_validate_doctor_status ON doctors;
CREATE TRIGGER trigger_validate_doctor_status
  BEFORE INSERT OR UPDATE ON doctors
  FOR EACH ROW EXECUTE FUNCTION validate_doctor_status();

-- Patients table triggers
DROP TRIGGER IF EXISTS trigger_validate_patient_data ON patients;
CREATE TRIGGER trigger_validate_patient_data
  BEFORE INSERT OR UPDATE ON patients
  FOR EACH ROW EXECUTE FUNCTION validate_patient_data();

-- Appointments table triggers
DROP TRIGGER IF EXISTS trigger_validate_appointment_data ON appointments;
CREATE TRIGGER trigger_validate_appointment_data
  BEFORE INSERT OR UPDATE ON appointments
  FOR EACH ROW EXECUTE FUNCTION validate_appointment_data();

-- Rooms table triggers
DROP TRIGGER IF EXISTS trigger_validate_room_data ON rooms;
CREATE TRIGGER trigger_validate_room_data
  BEFORE INSERT OR UPDATE ON rooms
  FOR EACH ROW EXECUTE FUNCTION validate_room_data();

-- Medical records table triggers
DROP TRIGGER IF EXISTS trigger_validate_medical_record_status ON medical_records;
CREATE TRIGGER trigger_validate_medical_record_status
  BEFORE INSERT OR UPDATE ON medical_records
  FOR EACH ROW EXECUTE FUNCTION validate_medical_record_status();

-- Prescriptions table triggers
DROP TRIGGER IF EXISTS trigger_validate_prescription_status ON prescriptions;
CREATE TRIGGER trigger_validate_prescription_status
  BEFORE INSERT OR UPDATE ON prescriptions
  FOR EACH ROW EXECUTE FUNCTION validate_prescription_status();

-- =====================================================
-- STEP 9: CREATE VIEWS FOR EASY ACCESS
-- =====================================================

-- View to get all enum categories with counts
CREATE OR REPLACE VIEW v_enum_categories AS
SELECT
  ec.category_id,
  ec.category_name,
  ec.display_name_en,
  ec.display_name_vi,
  ec.description,
  ec.is_system,
  ec.is_active,
  COUNT(se.enum_id) as enum_count,
  ec.created_at,
  ec.updated_at
FROM enum_categories ec
LEFT JOIN system_enums se ON ec.category_id = se.category_id AND se.is_active = true
WHERE ec.is_active = true
GROUP BY ec.category_id, ec.category_name, ec.display_name_en, ec.display_name_vi,
         ec.description, ec.is_system, ec.is_active, ec.created_at, ec.updated_at
ORDER BY ec.category_name;

-- View to get all active enums with category info
CREATE OR REPLACE VIEW v_system_enums AS
SELECT
  se.enum_id,
  se.category_id,
  ec.category_name,
  ec.display_name_en as category_display_en,
  ec.display_name_vi as category_display_vi,
  se.enum_key,
  se.display_name_en,
  se.display_name_vi,
  se.description_en,
  se.description_vi,
  se.sort_order,
  se.color_code,
  se.icon_name,
  se.is_default,
  se.is_system,
  se.metadata,
  se.created_at,
  se.updated_at
FROM system_enums se
JOIN enum_categories ec ON se.category_id = ec.category_id
WHERE se.is_active = true AND ec.is_active = true
ORDER BY ec.category_name, se.sort_order, se.display_name_en;

-- View for Vietnamese enum options (for UI)
CREATE OR REPLACE VIEW v_enum_options_vi AS
SELECT
  category_id,
  enum_key as value,
  display_name_vi as label,
  description_vi as description,
  color_code,
  icon_name,
  sort_order,
  is_default
FROM system_enums
WHERE is_active = true
ORDER BY category_id, sort_order, display_name_vi;

-- View for English enum options (for UI)
CREATE OR REPLACE VIEW v_enum_options_en AS
SELECT
  category_id,
  enum_key as value,
  display_name_en as label,
  description_en as description,
  color_code,
  icon_name,
  sort_order,
  is_default
FROM system_enums
WHERE is_active = true
ORDER BY category_id, sort_order, display_name_en;

-- =====================================================
-- STEP 10: VERIFICATION AND TESTING
-- =====================================================

-- Test enum functions
DO $$
DECLARE
  test_result TEXT;
  test_valid BOOLEAN;
  test_default TEXT;
BEGIN
  -- Test get_enum_display_name
  SELECT get_enum_display_name('ROLE', 'admin', 'vi') INTO test_result;
  IF test_result != 'Quản trị viên' THEN
    RAISE EXCEPTION 'Function get_enum_display_name failed. Expected: Quản trị viên, Got: %', test_result;
  END IF;

  -- Test validate_enum_value
  SELECT validate_enum_value('ROLE', 'admin') INTO test_valid;
  IF test_valid != true THEN
    RAISE EXCEPTION 'Function validate_enum_value failed for valid enum';
  END IF;

  SELECT validate_enum_value('ROLE', 'invalid') INTO test_valid;
  IF test_valid != false THEN
    RAISE EXCEPTION 'Function validate_enum_value failed for invalid enum';
  END IF;

  -- Test get_default_enum_value
  SELECT get_default_enum_value('ROLE') INTO test_default;
  IF test_default != 'patient' THEN
    RAISE EXCEPTION 'Function get_default_enum_value failed. Expected: patient, Got: %', test_default;
  END IF;

  RAISE NOTICE 'All enum functions tested successfully!';
END $$;

-- =====================================================
-- STEP 11: FINAL STATUS CHECK
-- =====================================================

-- Display setup completion status
SELECT
  '🎉 DYNAMIC ENUM SYSTEM SETUP COMPLETE!' as status,
  (SELECT COUNT(*) FROM enum_categories WHERE is_active = true) as categories_created,
  (SELECT COUNT(*) FROM system_enums WHERE is_active = true) as enums_created,
  (SELECT COUNT(*) FROM information_schema.routines WHERE routine_name LIKE '%enum%' AND routine_schema = 'public') as functions_created,
  (SELECT COUNT(*) FROM information_schema.triggers WHERE trigger_name LIKE '%validate%') as triggers_created;

-- Display enum categories summary
SELECT
  '📊 ENUM CATEGORIES SUMMARY' as summary_type,
  category_name,
  enum_count,
  CASE
    WHEN enum_count > 0 THEN '✅ READY'
    ELSE '❌ NO DATA'
  END as status
FROM v_enum_categories
ORDER BY category_name;

-- Display next steps
SELECT
  '🚀 NEXT STEPS' as section,
  'Frontend EnumProvider is ready to use' as step_1,
  'Use enum hooks: useRoleEnums(), useGenderEnums(), etc.' as step_2,
  'Admin can manage enums via EnumManager component' as step_3,
  'All enum values are automatically validated' as step_4;

-- Display usage examples
SELECT
  '💡 USAGE EXAMPLES' as section,
  'SELECT get_enum_display_name(''ROLE'', ''admin'', ''vi'');' as example_1,
  'SELECT validate_enum_value(''ROLE'', ''admin'');' as example_2,
  'SELECT * FROM get_enums_by_category(''ROLE'', ''vi'');' as example_3,
  'SELECT * FROM v_enum_options_vi WHERE category_id = ''ROLE'';' as example_4;

-- Final success message
SELECT
  '✅ SETUP COMPLETED SUCCESSFULLY!' as final_status,
  'The dynamic enum system is now ready for use.' as message,
  'You can now use enum hooks in your React components.' as frontend_ready,
  'All database validation is automatically handled.' as backend_ready;
