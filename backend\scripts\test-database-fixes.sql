-- =====================================================
-- TEST DATABASE FIXES SCRIPT
-- =====================================================
-- This script tests all the fixes applied to the database
-- Run this AFTER running the fix scripts

-- =====================================================
-- 1. TEST FOREIGN KEY CONSTRAINTS
-- =====================================================

SELECT 
  '=== TESTING FOREIGN KEY CONSTRAINTS ===' as test_section,
  '' as constraint_name,
  '' as status;

-- Check if FK constraints exist
SELECT 
  'Foreign Keys' as test_section,
  tc.constraint_name,
  CASE 
    WHEN tc.constraint_name IS NOT NULL THEN '✅ EXISTS'
    ELSE '❌ MISSING'
  END as status
FROM (VALUES 
  ('doctors_department_id_fkey'),
  ('appointments_room_id_fkey')
) AS expected_fks(constraint_name)
LEFT JOIN information_schema.table_constraints tc 
  ON tc.constraint_name = expected_fks.constraint_name
  AND tc.constraint_type = 'FOREIGN KEY'
  AND tc.table_schema = 'public';

-- =====================================================
-- 2. TEST ENUM VALIDATION
-- =====================================================

SELECT 
  '=== TESTING ENUM VALIDATION ===' as test_section,
  '' as test_name,
  '' as result;

-- Test 1: Valid enum value (should succeed)
DO $$
BEGIN
  -- This should work
  IF validate_enum_value('ROLE', 'patient') THEN
    RAISE NOTICE 'Test 1 PASSED: Valid enum validation works';
  ELSE
    RAISE NOTICE 'Test 1 FAILED: Valid enum validation failed';
  END IF;
END $$;

-- Test 2: Invalid enum value (should fail)
DO $$
BEGIN
  -- This should return false
  IF NOT validate_enum_value('ROLE', 'invalid_role') THEN
    RAISE NOTICE 'Test 2 PASSED: Invalid enum validation works';
  ELSE
    RAISE NOTICE 'Test 2 FAILED: Invalid enum validation passed incorrectly';
  END IF;
END $$;

-- =====================================================
-- 3. TEST INDEXES
-- =====================================================

SELECT 
  '=== TESTING INDEXES ===' as test_section,
  '' as index_name,
  '' as status;

-- Check if required indexes exist
SELECT 
  'Indexes' as test_section,
  indexname as index_name,
  '✅ EXISTS' as status
FROM pg_indexes 
WHERE schemaname = 'public'
  AND indexname IN (
    'idx_doctors_department_id',
    'idx_appointments_room_id',
    'idx_medical_records_appointment_id',
    'idx_appointments_doctor_datetime',
    'idx_appointments_patient_datetime'
  )
ORDER BY indexname;

-- =====================================================
-- 4. TEST CONSTRAINTS
-- =====================================================

SELECT 
  '=== TESTING CONSTRAINTS ===' as test_section,
  '' as constraint_name,
  '' as status;

-- Check if constraints exist
SELECT 
  'Constraints' as test_section,
  constraint_name,
  '✅ EXISTS' as status
FROM information_schema.check_constraints
WHERE constraint_schema = 'public'
  AND constraint_name IN (
    'appointments_datetime_future',
    'patients_birth_date_valid',
    'rooms_capacity_positive'
  );

-- =====================================================
-- 5. TEST TRIGGERS
-- =====================================================

SELECT 
  '=== TESTING TRIGGERS ===' as test_section,
  '' as trigger_name,
  '' as status;

-- Check if enum validation triggers exist
SELECT 
  'Triggers' as test_section,
  trigger_name,
  '✅ EXISTS' as status
FROM information_schema.triggers
WHERE trigger_schema = 'public'
  AND trigger_name IN (
    'validate_profiles_enum',
    'validate_doctors_enum',
    'validate_patients_enum',
    'validate_appointments_enum',
    'validate_rooms_enum',
    'prevent_appointment_conflicts_trigger'
  )
ORDER BY trigger_name;

-- =====================================================
-- 6. TEST VIEWS
-- =====================================================

SELECT 
  '=== TESTING VIEWS ===' as test_section,
  '' as view_name,
  '' as status;

-- Check if views exist
SELECT 
  'Views' as test_section,
  table_name as view_name,
  '✅ EXISTS' as status
FROM information_schema.views
WHERE table_schema = 'public'
  AND table_name IN (
    'v_doctors_complete',
    'v_patients_complete',
    'v_appointments_detailed',
    'v_enum_options'
  )
ORDER BY table_name;

-- =====================================================
-- 7. TEST FUNCTIONS
-- =====================================================

SELECT 
  '=== TESTING FUNCTIONS ===' as test_section,
  '' as function_name,
  '' as status;

-- Check if functions exist
SELECT 
  'Functions' as test_section,
  routine_name as function_name,
  '✅ EXISTS' as status
FROM information_schema.routines
WHERE routine_schema = 'public'
  AND routine_type = 'FUNCTION'
  AND routine_name IN (
    'validate_enum_value',
    'validate_enum_fields',
    'get_available_slots',
    'get_doctor_stats',
    'check_appointment_conflict',
    'get_default_enum_value',
    'prevent_appointment_conflicts'
  )
ORDER BY routine_name;

-- =====================================================
-- 8. TEST ENUM SYSTEM
-- =====================================================

SELECT 
  '=== TESTING ENUM SYSTEM ===' as test_section,
  '' as category_id,
  '' as enum_count;

-- Check enum categories and counts
SELECT 
  'Enum System' as test_section,
  ec.category_id,
  COUNT(se.enum_id)::TEXT || ' enums' as enum_count
FROM enum_categories ec
LEFT JOIN system_enums se ON ec.category_id = se.category_id AND se.is_active = true
WHERE ec.is_active = true
GROUP BY ec.category_id, ec.display_name_vi
ORDER BY ec.category_id;

-- =====================================================
-- 9. TEST RLS POLICIES
-- =====================================================

SELECT 
  '=== TESTING RLS POLICIES ===' as test_section,
  '' as table_name,
  '' as policy_count;

-- Check RLS policies count per table
SELECT 
  'RLS Policies' as test_section,
  tablename as table_name,
  COUNT(*)::TEXT || ' policies' as policy_count
FROM pg_policies
WHERE schemaname = 'public'
GROUP BY tablename
ORDER BY tablename;

-- =====================================================
-- 10. PERFORMANCE TEST QUERIES
-- =====================================================

SELECT 
  '=== PERFORMANCE TEST QUERIES ===' as test_section,
  '' as query_type,
  '' as execution_time;

-- Test query performance (these should be fast with indexes)
EXPLAIN (ANALYZE, BUFFERS) 
SELECT d.doctor_id, d.specialization, dept.name 
FROM doctors d 
LEFT JOIN departments dept ON d.department_id = dept.department_id 
WHERE d.status = 'active'
LIMIT 10;

-- =====================================================
-- 11. DATA INTEGRITY CHECKS
-- =====================================================

SELECT 
  '=== DATA INTEGRITY CHECKS ===' as test_section,
  '' as check_type,
  '' as result;

-- Check for orphaned records
SELECT 
  'Orphaned Records' as test_section,
  'Doctors without profiles' as check_type,
  CASE 
    WHEN COUNT(*) = 0 THEN '✅ NO ORPHANS'
    ELSE '❌ ' || COUNT(*)::TEXT || ' ORPHANED'
  END as result
FROM doctors d
LEFT JOIN profiles p ON d.profile_id = p.id
WHERE p.id IS NULL;

SELECT 
  'Orphaned Records' as test_section,
  'Patients without profiles' as check_type,
  CASE 
    WHEN COUNT(*) = 0 THEN '✅ NO ORPHANS'
    ELSE '❌ ' || COUNT(*)::TEXT || ' ORPHANED'
  END as result
FROM patients pt
LEFT JOIN profiles p ON pt.profile_id = p.id
WHERE p.id IS NULL;

-- Check for invalid enum values
SELECT 
  'Invalid Enums' as test_section,
  'Profiles with invalid roles' as check_type,
  CASE 
    WHEN COUNT(*) = 0 THEN '✅ ALL VALID'
    ELSE '❌ ' || COUNT(*)::TEXT || ' INVALID'
  END as result
FROM profiles p
WHERE p.role IS NOT NULL 
  AND NOT EXISTS (
    SELECT 1 FROM system_enums se 
    WHERE se.category_id = 'ROLE' 
      AND se.enum_key = p.role 
      AND se.is_active = true
  );

-- =====================================================
-- 12. SUMMARY REPORT
-- =====================================================

SELECT 
  '=== SUMMARY REPORT ===' as test_section,
  '' as metric,
  '' as value;

SELECT 
  'Summary' as test_section,
  'Total Tables' as metric,
  COUNT(*)::TEXT as value
FROM information_schema.tables 
WHERE table_schema = 'public' AND table_type = 'BASE TABLE';

SELECT 
  'Summary' as test_section,
  'Total Views' as metric,
  COUNT(*)::TEXT as value
FROM information_schema.views 
WHERE table_schema = 'public';

SELECT 
  'Summary' as test_section,
  'Total Functions' as metric,
  COUNT(*)::TEXT as value
FROM information_schema.routines 
WHERE routine_schema = 'public' AND routine_type = 'FUNCTION';

SELECT 
  'Summary' as test_section,
  'Total Triggers' as metric,
  COUNT(*)::TEXT as value
FROM information_schema.triggers 
WHERE trigger_schema = 'public';

SELECT 
  'Summary' as test_section,
  'Total Indexes' as metric,
  COUNT(*)::TEXT as value
FROM pg_indexes 
WHERE schemaname = 'public';

-- =====================================================
-- 13. FINAL STATUS
-- =====================================================

SELECT 
  '✅ DATABASE TESTING COMPLETED!' as final_status,
  'Review the results above to ensure all fixes are working' as message,
  'Your database is now optimized and secure' as conclusion;
