-- =====================================================
-- COMPREHENSIVE DATABASE ANALYSIS SCRIPT
-- =====================================================
-- This script analyzes the current database structure, relationships, 
-- constraints, and identifies potential issues

-- =====================================================
-- 1. TABLE EXISTENCE CHECK
-- =====================================================

SELECT 
  '=== TABLE EXISTENCE CHECK ===' as section,
  '' as table_name,
  '' as status,
  '' as issue;

SELECT 
  'Core Tables' as section,
  table_name,
  CASE 
    WHEN table_name IN (
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    ) THEN '✅ EXISTS'
    ELSE '❌ MISSING'
  END as status,
  CASE 
    WHEN table_name NOT IN (
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    ) THEN 'Table needs to be created'
    ELSE 'OK'
  END as issue
FROM (VALUES 
  ('profiles'),
  ('admins'),
  ('doctors'),
  ('patients'),
  ('departments'),
  ('appointments'),
  ('rooms'),
  ('medical_records'),
  ('prescriptions'),
  ('enum_categories'),
  ('system_enums'),
  ('audit_logs')
) AS expected_tables(table_name);

-- =====================================================
-- 2. FOREIGN KEY RELATIONSHIPS CHECK
-- =====================================================

SELECT 
  '=== FOREIGN KEY RELATIONSHIPS ===' as section,
  '' as constraint_name,
  '' as table_name,
  '' as referenced_table,
  '' as status;

SELECT 
  'Foreign Keys' as section,
  tc.constraint_name,
  tc.table_name,
  ccu.table_name AS referenced_table,
  '✅ EXISTS' as status
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
  ON tc.constraint_name = kcu.constraint_name
  AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
  ON ccu.constraint_name = tc.constraint_name
  AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
  AND tc.table_schema = 'public'
ORDER BY tc.table_name, tc.constraint_name;

-- =====================================================
-- 3. MISSING FOREIGN KEYS CHECK
-- =====================================================

SELECT 
  '=== MISSING FOREIGN KEYS CHECK ===' as section,
  '' as table_name,
  '' as column_name,
  '' as should_reference,
  '' as issue;

-- Check if doctors.department_id references departments
SELECT 
  'Missing FK' as section,
  'doctors' as table_name,
  'department_id' as column_name,
  'departments(department_id)' as should_reference,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM information_schema.table_constraints tc
      JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
      WHERE tc.table_name = 'doctors' 
        AND kcu.column_name = 'department_id'
        AND tc.constraint_type = 'FOREIGN KEY'
    ) THEN '✅ FK EXISTS'
    ELSE '❌ MISSING FK'
  END as issue
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'doctors' AND table_schema = 'public');

-- Check if appointments.room_id references rooms
SELECT 
  'Missing FK' as section,
  'appointments' as table_name,
  'room_id' as column_name,
  'rooms(room_id)' as should_reference,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM information_schema.table_constraints tc
      JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
      WHERE tc.table_name = 'appointments' 
        AND kcu.column_name = 'room_id'
        AND tc.constraint_type = 'FOREIGN KEY'
    ) THEN '✅ FK EXISTS'
    ELSE '❌ MISSING FK'
  END as issue
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'appointments' AND table_schema = 'public');

-- =====================================================
-- 4. ENUM VALIDATION CHECK
-- =====================================================

SELECT 
  '=== ENUM VALIDATION CHECK ===' as section,
  '' as table_name,
  '' as column_name,
  '' as enum_category,
  '' as validation_status;

-- Check if enum validation triggers exist
SELECT 
  'Enum Validation' as section,
  table_name,
  column_name,
  expected_category,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM information_schema.triggers 
      WHERE event_object_table = table_name
        AND trigger_name LIKE '%enum%'
    ) THEN '✅ TRIGGER EXISTS'
    ELSE '❌ NO VALIDATION'
  END as validation_status
FROM (VALUES 
  ('profiles', 'role', 'ROLE'),
  ('doctors', 'status', 'DOCTOR_STATUS'),
  ('patients', 'gender', 'GENDER'),
  ('patients', 'blood_type', 'BLOOD_TYPE'),
  ('patients', 'status', 'PATIENT_STATUS'),
  ('appointments', 'type', 'APPOINTMENT_TYPE'),
  ('appointments', 'status', 'APPOINTMENT_STATUS'),
  ('rooms', 'room_type', 'ROOM_TYPE'),
  ('rooms', 'status', 'ROOM_STATUS'),
  ('medical_records', 'status', 'MEDICAL_RECORD_STATUS'),
  ('prescriptions', 'status', 'PRESCRIPTION_STATUS')
) AS enum_fields(table_name, column_name, expected_category)
WHERE EXISTS (
  SELECT 1 FROM information_schema.tables 
  WHERE table_name = enum_fields.table_name AND table_schema = 'public'
);

-- =====================================================
-- 5. COLUMN CONSTRAINTS CHECK
-- =====================================================

SELECT 
  '=== COLUMN CONSTRAINTS CHECK ===' as section,
  '' as table_name,
  '' as column_name,
  '' as constraint_type,
  '' as status;

SELECT 
  'Constraints' as section,
  tc.table_name,
  kcu.column_name,
  tc.constraint_type,
  '✅ EXISTS' as status
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu 
  ON tc.constraint_name = kcu.constraint_name
WHERE tc.table_schema = 'public'
  AND tc.constraint_type IN ('PRIMARY KEY', 'UNIQUE', 'CHECK')
ORDER BY tc.table_name, tc.constraint_type, kcu.column_name;

-- =====================================================
-- 6. INDEX ANALYSIS
-- =====================================================

SELECT 
  '=== INDEX ANALYSIS ===' as section,
  '' as table_name,
  '' as index_name,
  '' as columns,
  '' as status;

SELECT 
  'Indexes' as section,
  schemaname || '.' || tablename as table_name,
  indexname as index_name,
  indexdef as columns,
  '✅ EXISTS' as status
FROM pg_indexes 
WHERE schemaname = 'public'
ORDER BY tablename, indexname;

-- =====================================================
-- 7. ROW LEVEL SECURITY CHECK
-- =====================================================

SELECT 
  '=== ROW LEVEL SECURITY CHECK ===' as section,
  '' as table_name,
  '' as rls_enabled,
  '' as policies_count,
  '' as status;

SELECT 
  'RLS Status' as section,
  tablename as table_name,
  CASE WHEN rowsecurity THEN '✅ ENABLED' ELSE '❌ DISABLED' END as rls_enabled,
  (
    SELECT COUNT(*)::text 
    FROM pg_policies 
    WHERE tablename = c.tablename
  ) as policies_count,
  CASE 
    WHEN rowsecurity AND EXISTS (
      SELECT 1 FROM pg_policies WHERE tablename = c.tablename
    ) THEN '✅ PROPERLY CONFIGURED'
    WHEN rowsecurity THEN '⚠️ ENABLED BUT NO POLICIES'
    ELSE '❌ NOT SECURED'
  END as status
FROM pg_tables c
WHERE schemaname = 'public'
ORDER BY tablename;

-- =====================================================
-- 8. DATA INTEGRITY ISSUES
-- =====================================================

SELECT 
  '=== DATA INTEGRITY ISSUES ===' as section,
  '' as issue_type,
  '' as description,
  '' as count,
  '' as severity;

-- Check for orphaned records (if tables exist)
DO $$
BEGIN
  -- Check orphaned doctors (no profile)
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'doctors' AND table_schema = 'public') THEN
    PERFORM 1;
  END IF;
END $$;

-- =====================================================
-- 9. PERFORMANCE RECOMMENDATIONS
-- =====================================================

SELECT 
  '=== PERFORMANCE RECOMMENDATIONS ===' as section,
  '' as table_name,
  '' as recommendation,
  '' as priority,
  '' as reason;

-- Check for missing indexes on foreign keys
SELECT 
  'Performance' as section,
  kcu.table_name,
  'Add index on ' || kcu.column_name as recommendation,
  'HIGH' as priority,
  'Foreign key without index' as reason
FROM information_schema.key_column_usage kcu
JOIN information_schema.table_constraints tc 
  ON kcu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY'
  AND tc.table_schema = 'public'
  AND NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE tablename = kcu.table_name 
      AND indexdef LIKE '%' || kcu.column_name || '%'
  );

-- =====================================================
-- 10. SUMMARY REPORT
-- =====================================================

SELECT 
  '=== SUMMARY REPORT ===' as section,
  '' as metric,
  '' as value,
  '' as status;

SELECT 
  'Summary' as section,
  'Total Tables' as metric,
  COUNT(*)::text as value,
  '✅ INFO' as status
FROM information_schema.tables 
WHERE table_schema = 'public';

SELECT 
  'Summary' as section,
  'Total Foreign Keys' as metric,
  COUNT(*)::text as value,
  '✅ INFO' as status
FROM information_schema.table_constraints 
WHERE constraint_type = 'FOREIGN KEY' 
  AND table_schema = 'public';

SELECT 
  'Summary' as section,
  'Total Indexes' as metric,
  COUNT(*)::text as value,
  '✅ INFO' as status
FROM pg_indexes 
WHERE schemaname = 'public';

SELECT 
  'Summary' as section,
  'RLS Enabled Tables' as metric,
  COUNT(*)::text as value,
  CASE 
    WHEN COUNT(*) > 0 THEN '✅ GOOD'
    ELSE '❌ NEEDS ATTENTION'
  END as status
FROM pg_tables 
WHERE schemaname = 'public' 
  AND rowsecurity = true;
