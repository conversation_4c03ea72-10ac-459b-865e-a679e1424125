-- =====================================================
-- CHECK AND UPDATE EXISTING DATABASE FOR ENUM SYSTEM
-- =====================================================
-- This script checks your existing Supabase database and updates it to support dynamic enums
-- Run this script to integrate enum system with your existing data

-- =====================================================
-- 1. CHECK EXISTING TABLES
-- =====================================================

-- Check what tables already exist
SELECT 
  'Existing Tables Check' as check_type,
  table_name,
  CASE 
    WHEN table_name IN ('enum_categories', 'system_enums') THEN 'ENUM SYSTEM'
    WHEN table_name IN ('profiles', 'doctors', 'patients', 'appointments', 'rooms', 'medical_records', 'prescriptions') THEN 'CORE TABLES'
    WHEN table_name IN ('departments', 'audit_logs') THEN 'SUPPORTING TABLES'
    ELSE 'OTHER'
  END as table_category
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_category, table_name;

-- =====================================================
-- 2. CREATE ENUM TABLES IF NOT EXISTS
-- =====================================================

-- Create enum_categories table
CREATE TABLE IF NOT EXISTS enum_categories (
  category_id TEXT PRIMARY KEY,
  category_name TEXT UNIQUE NOT NULL,
  display_name_en TEXT NOT NULL,
  display_name_vi TEXT NOT NULL,
  description TEXT,
  is_system BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create system_enums table
CREATE TABLE IF NOT EXISTS system_enums (
  enum_id TEXT PRIMARY KEY DEFAULT ('ENUM' || EXTRACT(EPOCH FROM NOW())::BIGINT),
  category_id TEXT NOT NULL REFERENCES enum_categories(category_id) ON DELETE CASCADE,
  enum_key TEXT NOT NULL,
  display_name_en TEXT NOT NULL,
  display_name_vi TEXT NOT NULL,
  description_en TEXT,
  description_vi TEXT,
  sort_order INTEGER DEFAULT 0,
  color_code TEXT,
  icon_name TEXT,
  is_default BOOLEAN DEFAULT false,
  is_system BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(category_id, enum_key)
);

-- =====================================================
-- 3. ENABLE RLS FOR ENUM TABLES
-- =====================================================

-- Enable RLS for enum_categories
ALTER TABLE enum_categories ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Everyone can view active enum categories" ON enum_categories;
DROP POLICY IF EXISTS "Admins can manage enum categories" ON enum_categories;

-- Create RLS policies for enum_categories
CREATE POLICY "Everyone can view active enum categories" ON enum_categories
  FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage enum categories" ON enum_categories
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- Enable RLS for system_enums
ALTER TABLE system_enums ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Everyone can view active enums" ON system_enums;
DROP POLICY IF EXISTS "Admins can manage enums" ON system_enums;

-- Create RLS policies for system_enums
CREATE POLICY "Everyone can view active enums" ON system_enums
  FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage enums" ON system_enums
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- =====================================================
-- 4. CREATE INDEXES FOR ENUM TABLES
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_system_enums_category ON system_enums(category_id);
CREATE INDEX IF NOT EXISTS idx_system_enums_key ON system_enums(enum_key);
CREATE INDEX IF NOT EXISTS idx_system_enums_active ON system_enums(is_active);
CREATE INDEX IF NOT EXISTS idx_system_enums_sort ON system_enums(category_id, sort_order);

-- =====================================================
-- 5. CREATE ENUM FUNCTIONS
-- =====================================================

-- Function to get enum display name by language
CREATE OR REPLACE FUNCTION get_enum_display_name(
  p_category_id TEXT,
  p_enum_key TEXT,
  p_language TEXT DEFAULT 'vi'
)
RETURNS TEXT AS $$
DECLARE
  result TEXT;
BEGIN
  IF p_language = 'en' THEN
    SELECT display_name_en INTO result
    FROM system_enums
    WHERE category_id = p_category_id 
      AND enum_key = p_enum_key 
      AND is_active = true;
  ELSE
    SELECT display_name_vi INTO result
    FROM system_enums
    WHERE category_id = p_category_id 
      AND enum_key = p_enum_key 
      AND is_active = true;
  END IF;
  
  RETURN COALESCE(result, p_enum_key);
END;
$$ LANGUAGE plpgsql;

-- Function to validate enum value
CREATE OR REPLACE FUNCTION validate_enum_value(
  p_category_id TEXT,
  p_enum_key TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM system_enums 
    WHERE category_id = p_category_id 
      AND enum_key = p_enum_key 
      AND is_active = true
  );
END;
$$ LANGUAGE plpgsql;

-- Function to get default enum value for a category
CREATE OR REPLACE FUNCTION get_default_enum_value(p_category_id TEXT)
RETURNS TEXT AS $$
DECLARE
  result TEXT;
BEGIN
  SELECT enum_key INTO result
  FROM system_enums
  WHERE category_id = p_category_id 
    AND is_default = true 
    AND is_active = true
  LIMIT 1;
  
  IF result IS NULL THEN
    SELECT enum_key INTO result
    FROM system_enums
    WHERE category_id = p_category_id 
      AND is_active = true
    ORDER BY sort_order, enum_key
    LIMIT 1;
  END IF;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Function to get all enums for a category
CREATE OR REPLACE FUNCTION get_enums_by_category(
  p_category_id TEXT,
  p_language TEXT DEFAULT 'vi'
)
RETURNS TABLE(
  enum_key TEXT,
  display_name TEXT,
  description TEXT,
  sort_order INTEGER,
  color_code TEXT,
  icon_name TEXT,
  is_default BOOLEAN
) AS $$
BEGIN
  IF p_language = 'en' THEN
    RETURN QUERY
    SELECT 
      se.enum_key,
      se.display_name_en as display_name,
      se.description_en as description,
      se.sort_order,
      se.color_code,
      se.icon_name,
      se.is_default
    FROM system_enums se
    WHERE se.category_id = p_category_id 
      AND se.is_active = true
    ORDER BY se.sort_order, se.display_name_en;
  ELSE
    RETURN QUERY
    SELECT 
      se.enum_key,
      se.display_name_vi as display_name,
      se.description_vi as description,
      se.sort_order,
      se.color_code,
      se.icon_name,
      se.is_default
    FROM system_enums se
    WHERE se.category_id = p_category_id 
      AND se.is_active = true
    ORDER BY se.sort_order, se.display_name_vi;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 6. CREATE ENUM TRIGGERS FOR UPDATED_AT
-- =====================================================

-- Trigger function for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add triggers for enum tables
DROP TRIGGER IF EXISTS update_enum_categories_updated_at ON enum_categories;
CREATE TRIGGER update_enum_categories_updated_at
  BEFORE UPDATE ON enum_categories
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_system_enums_updated_at ON system_enums;
CREATE TRIGGER update_system_enums_updated_at
  BEFORE UPDATE ON system_enums
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 7. CREATE VIEWS FOR EASY ACCESS
-- =====================================================

-- View to get all enum categories with counts
CREATE OR REPLACE VIEW v_enum_categories AS
SELECT 
  ec.category_id,
  ec.category_name,
  ec.display_name_en,
  ec.display_name_vi,
  ec.description,
  ec.is_system,
  ec.is_active,
  COUNT(se.enum_id) as enum_count,
  ec.created_at,
  ec.updated_at
FROM enum_categories ec
LEFT JOIN system_enums se ON ec.category_id = se.category_id AND se.is_active = true
WHERE ec.is_active = true
GROUP BY ec.category_id, ec.category_name, ec.display_name_en, ec.display_name_vi, 
         ec.description, ec.is_system, ec.is_active, ec.created_at, ec.updated_at
ORDER BY ec.category_name;

-- View to get all active enums with category info
CREATE OR REPLACE VIEW v_system_enums AS
SELECT 
  se.enum_id,
  se.category_id,
  ec.category_name,
  ec.display_name_en as category_display_en,
  ec.display_name_vi as category_display_vi,
  se.enum_key,
  se.display_name_en,
  se.display_name_vi,
  se.description_en,
  se.description_vi,
  se.sort_order,
  se.color_code,
  se.icon_name,
  se.is_default,
  se.is_system,
  se.metadata,
  se.created_at,
  se.updated_at
FROM system_enums se
JOIN enum_categories ec ON se.category_id = ec.category_id
WHERE se.is_active = true AND ec.is_active = true
ORDER BY ec.category_name, se.sort_order, se.display_name_en;

-- =====================================================
-- 8. VERIFICATION
-- =====================================================

-- Check if enum system is ready
SELECT 
  'Enum System Setup Status' as check_type,
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'enum_categories') 
     AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'system_enums')
    THEN 'READY - Tables created'
    ELSE 'ERROR - Tables missing'
  END as status,
  (SELECT COUNT(*) FROM information_schema.routines WHERE routine_name LIKE '%enum%') as functions_count;

-- Show next steps
SELECT 
  'Next Steps' as info,
  'Run populate-enum-data.sql to add enum values' as action_required;
