const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const log = (color, message) => {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color] || ''}${message}${colors.reset}`);
};

async function checkDatabaseStructure() {
  try {
    log('cyan', '🔍 HOSPITAL MANAGEMENT DATABASE ANALYSIS');
    log('cyan', '==========================================');
    
    const supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );
    
    // 1. Check table existence
    log('blue', '\n📋 1. CHECKING TABLE EXISTENCE...');
    const expectedTables = [
      'profiles', 'admins', 'doctors', 'patients', 'departments',
      'appointments', 'rooms', 'medical_records', 'prescriptions',
      'enum_categories', 'system_enums', 'audit_logs'
    ];
    
    const existingTables = [];
    const missingTables = [];
    
    for (const tableName of expectedTables) {
      try {
        const { count, error } = await supabase
          .from(tableName)
          .select('*', { count: 'exact', head: true });
        
        if (!error) {
          existingTables.push(tableName);
          log('green', `  ✅ ${tableName}: ${count || 0} records`);
        } else {
          missingTables.push(tableName);
          log('red', `  ❌ ${tableName}: ${error.message}`);
        }
      } catch (err) {
        missingTables.push(tableName);
        log('red', `  ❌ ${tableName}: Table not accessible`);
      }
    }
    
    // 2. Check enum system
    log('blue', '\n🔧 2. CHECKING ENUM SYSTEM...');
    if (existingTables.includes('enum_categories') && existingTables.includes('system_enums')) {
      const { data: categories, error: catError } = await supabase
        .from('enum_categories')
        .select('*');
      
      if (!catError && categories) {
        log('green', `  ✅ Enum categories: ${categories.length} found`);
        categories.forEach(cat => {
          log('blue', `    - ${cat.category_id}: ${cat.display_name_vi}`);
        });
        
        const { data: enums, error: enumError } = await supabase
          .from('system_enums')
          .select('*');
        
        if (!enumError && enums) {
          log('green', `  ✅ System enums: ${enums.length} found`);
        }
      } else {
        log('red', '  ❌ Enum system not properly configured');
      }
    } else {
      log('red', '  ❌ Enum system tables missing');
    }
    
    // 3. Check critical relationships
    log('blue', '\n🔗 3. CHECKING CRITICAL RELATIONSHIPS...');
    
    // Check profiles -> doctors relationship
    if (existingTables.includes('profiles') && existingTables.includes('doctors')) {
      const { data: doctorProfiles, error } = await supabase
        .from('doctors')
        .select(`
          doctor_id,
          profile_id,
          profiles!inner(full_name, email, role)
        `);
      
      if (!error) {
        log('green', `  ✅ Doctors-Profiles relationship: ${doctorProfiles?.length || 0} linked`);
      } else {
        log('red', `  ❌ Doctors-Profiles relationship issue: ${error.message}`);
      }
    }
    
    // Check profiles -> patients relationship
    if (existingTables.includes('profiles') && existingTables.includes('patients')) {
      const { data: patientProfiles, error } = await supabase
        .from('patients')
        .select(`
          patient_id,
          profile_id,
          profiles!inner(full_name, email, role)
        `);
      
      if (!error) {
        log('green', `  ✅ Patients-Profiles relationship: ${patientProfiles?.length || 0} linked`);
      } else {
        log('red', `  ❌ Patients-Profiles relationship issue: ${error.message}`);
      }
    }
    
    // 4. Check data consistency
    log('blue', '\n📊 4. CHECKING DATA CONSISTENCY...');
    
    // Check for orphaned records
    if (existingTables.includes('doctors')) {
      const { data: orphanedDoctors, error } = await supabase
        .from('doctors')
        .select('doctor_id, profile_id')
        .is('profiles.id', null)
        .leftJoin('profiles', 'doctors.profile_id', 'profiles.id');
      
      if (!error) {
        if (orphanedDoctors && orphanedDoctors.length > 0) {
          log('yellow', `  ⚠️ Found ${orphanedDoctors.length} orphaned doctor records`);
        } else {
          log('green', '  ✅ No orphaned doctor records');
        }
      }
    }
    
    // 5. Check enum validation
    log('blue', '\n🎯 5. CHECKING ENUM VALIDATION...');
    
    const enumFields = [
      { table: 'profiles', field: 'role', category: 'ROLE' },
      { table: 'doctors', field: 'status', category: 'DOCTOR_STATUS' },
      { table: 'patients', field: 'gender', category: 'GENDER' },
      { table: 'patients', field: 'blood_type', category: 'BLOOD_TYPE' },
      { table: 'appointments', field: 'status', category: 'APPOINTMENT_STATUS' },
      { table: 'rooms', field: 'status', category: 'ROOM_STATUS' }
    ];
    
    for (const { table, field, category } of enumFields) {
      if (existingTables.includes(table)) {
        // Check if there are invalid enum values
        const { data: records, error } = await supabase
          .from(table)
          .select(field)
          .not(field, 'is', null);
        
        if (!error && records) {
          const uniqueValues = [...new Set(records.map(r => r[field]))];
          log('blue', `  📋 ${table}.${field}: ${uniqueValues.join(', ')}`);
          
          // Check if these values exist in enum system
          if (existingTables.includes('system_enums')) {
            const { data: validEnums, error: enumError } = await supabase
              .from('system_enums')
              .select('enum_key')
              .eq('category_id', category);
            
            if (!enumError && validEnums) {
              const validKeys = validEnums.map(e => e.enum_key);
              const invalidValues = uniqueValues.filter(v => !validKeys.includes(v));
              
              if (invalidValues.length > 0) {
                log('yellow', `    ⚠️ Invalid values: ${invalidValues.join(', ')}`);
              } else {
                log('green', `    ✅ All values are valid`);
              }
            }
          }
        }
      }
    }
    
    // 6. Summary
    log('cyan', '\n📈 SUMMARY REPORT');
    log('cyan', '================');
    log('green', `✅ Tables found: ${existingTables.length}/${expectedTables.length}`);
    if (missingTables.length > 0) {
      log('red', `❌ Missing tables: ${missingTables.join(', ')}`);
    }
    
    const enumSystemReady = existingTables.includes('enum_categories') && existingTables.includes('system_enums');
    log(enumSystemReady ? 'green' : 'red', `${enumSystemReady ? '✅' : '❌'} Enum system: ${enumSystemReady ? 'Ready' : 'Not configured'}`);
    
    // Recommendations
    log('cyan', '\n💡 RECOMMENDATIONS');
    log('cyan', '==================');
    
    if (missingTables.length > 0) {
      log('yellow', '1. Run setup-supabase-tables.sql to create missing tables');
    }
    
    if (!enumSystemReady) {
      log('yellow', '2. Set up enum system by running populate-enum-data.sql');
    }
    
    log('yellow', '3. Consider adding enum validation triggers');
    log('yellow', '4. Verify Row Level Security policies are properly configured');
    log('yellow', '5. Add missing foreign key constraints where needed');
    
    log('green', '\n✅ Database analysis completed!');
    
  } catch (error) {
    log('red', `❌ Analysis failed: ${error.message}`);
  }
}

checkDatabaseStructure();
