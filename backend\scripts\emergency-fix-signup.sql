-- =====================================================
-- EMERGENCY FIX FOR SIGNUP ISSUE
-- =====================================================
-- This script temporarily disables RLS for profiles table
-- to allow signup to work, then re-enables with proper policies

-- Step 1: Temporarily disable RLS for profiles
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- Step 2: Ensure trigger function is correct
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert into profiles table
  INSERT INTO public.profiles (
    id, 
    email, 
    full_name, 
    phone_number,
    role,
    email_verified,
    is_active,
    created_at,
    updated_at
  )
  VALUES (
    NEW.id, 
    NEW.email, 
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email), 
    NEW.raw_user_meta_data->>'phone_number',
    COALESCE(NEW.raw_user_meta_data->>'role', 'patient'),
    CASE WHEN NEW.email_confirmed_at IS NOT NULL THEN true ELSE false END,
    true,
    NOW(),
    NOW()
  );
  
  -- If role is doctor, also insert into doctors table
  IF COALESCE(NEW.raw_user_meta_data->>'role', 'patient') = 'doctor' THEN
    INSERT INTO public.doctors (
      user_id,
      doctor_id,
      license_number,
      specialization,
      department_id,
      created_at,
      updated_at
    )
    VALUES (
      NEW.id,
      NEW.raw_user_meta_data->>'doctor_id',
      NEW.raw_user_meta_data->>'license_number',
      NEW.raw_user_meta_data->>'specialization',
      (NEW.raw_user_meta_data->>'department_id')::uuid,
      NOW(),
      NOW()
    );
  END IF;
  
  -- If role is patient, also insert into patients table
  IF COALESCE(NEW.raw_user_meta_data->>'role', 'patient') = 'patient' THEN
    INSERT INTO public.patients (
      user_id,
      patient_id,
      date_of_birth,
      gender,
      address,
      emergency_contact,
      created_at,
      updated_at
    )
    VALUES (
      NEW.id,
      NEW.raw_user_meta_data->>'patient_id',
      (NEW.raw_user_meta_data->>'date_of_birth')::date,
      NEW.raw_user_meta_data->>'gender',
      NEW.raw_user_meta_data->>'address',
      NEW.raw_user_meta_data->>'emergency_contact',
      NOW(),
      NOW()
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 3: Ensure trigger exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Step 4: Re-enable RLS with proper policies
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Step 5: Create comprehensive policies
DROP POLICY IF EXISTS "Allow profile creation during signup" ON profiles;
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Admins can view all profiles" ON profiles;

-- Allow anyone to insert (for signup trigger)
CREATE POLICY "Allow profile creation during signup" ON profiles
  FOR INSERT WITH CHECK (true);

-- Users can view their own profile
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

-- Admins can view all profiles
CREATE POLICY "Admins can view all profiles" ON profiles
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Step 6: Ensure doctors and patients tables have proper policies
ALTER TABLE doctors ENABLE ROW LEVEL SECURITY;
ALTER TABLE patients ENABLE ROW LEVEL SECURITY;

-- Doctors table policies
DROP POLICY IF EXISTS "Allow doctor profile creation" ON doctors;
DROP POLICY IF EXISTS "Doctors can view own profile" ON doctors;
DROP POLICY IF EXISTS "Admins can view all doctors" ON doctors;

CREATE POLICY "Allow doctor profile creation" ON doctors
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Doctors can view own profile" ON doctors
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all doctors" ON doctors
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Patients table policies
DROP POLICY IF EXISTS "Allow patient profile creation" ON patients;
DROP POLICY IF EXISTS "Patients can view own profile" ON patients;
DROP POLICY IF EXISTS "Admins can view all patients" ON patients;

CREATE POLICY "Allow patient profile creation" ON patients
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Patients can view own profile" ON patients
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all patients" ON patients
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Step 7: Verification
SELECT 'Emergency signup fix completed successfully!' as status;

-- Show current policies
SELECT 
  schemaname,
  tablename,
  policyname,
  cmd
FROM pg_policies 
WHERE tablename IN ('profiles', 'doctors', 'patients')
ORDER BY tablename, policyname;
