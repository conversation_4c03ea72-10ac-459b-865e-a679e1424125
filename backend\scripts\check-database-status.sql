-- =====================================================
-- CHECK DATABASE STATUS FOR ENUM SYSTEM
-- =====================================================
-- This script checks the current status of your Supabase database
-- and provides recommendations for enum system integration

-- =====================================================
-- 1. CHECK EXISTING TABLES
-- =====================================================

SELECT 
  '=== EXISTING TABLES ===' as section,
  '' as table_name,
  '' as status,
  '' as recommendation;

SELECT 
  'Core Tables' as section,
  table_name,
  'EXISTS' as status,
  CASE 
    WHEN table_name = 'profiles' THEN 'Check for role column with CHECK constraint'
    WHEN table_name = 'doctors' THEN 'Check for status column with CHECK constraint'
    WHEN table_name = 'patients' THEN 'Check for gender, blood_type, status columns'
    WHEN table_name = 'appointments' THEN 'Check for type, status columns'
    WHEN table_name = 'rooms' THEN 'Check for room_type, status columns'
    WHEN table_name = 'medical_records' THEN 'Check for status column'
    WHEN table_name = 'prescriptions' THEN 'Check for status column'
    ELSE 'No enum fields expected'
  END as recommendation
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name IN ('profiles', 'doctors', 'patients', 'appointments', 'rooms', 'medical_records', 'prescriptions', 'departments')
ORDER BY table_name;

-- =====================================================
-- 2. CHECK ENUM SYSTEM TABLES
-- =====================================================

SELECT 
  '=== ENUM SYSTEM STATUS ===' as section,
  '' as table_name,
  '' as status,
  '' as recommendation;

SELECT 
  'Enum System' as section,
  table_name,
  'EXISTS' as status,
  'Enum system is ready' as recommendation
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name IN ('enum_categories', 'system_enums')

UNION ALL

SELECT 
  'Enum System' as section,
  missing_table as table_name,
  'MISSING' as status,
  'Run check-and-update-existing-database.sql' as recommendation
FROM (
  SELECT 'enum_categories' as missing_table
  WHERE NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'enum_categories')
  
  UNION ALL
  
  SELECT 'system_enums' as missing_table
  WHERE NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'system_enums')
) missing;

-- =====================================================
-- 3. CHECK EXISTING CHECK CONSTRAINTS
-- =====================================================

SELECT 
  '=== CHECK CONSTRAINTS ===' as section,
  '' as table_name,
  '' as constraint_name,
  '' as recommendation;

SELECT 
  'Constraints to Remove' as section,
  tc.table_name,
  tc.constraint_name,
  'Remove this constraint after enum migration' as recommendation
FROM information_schema.table_constraints tc
JOIN information_schema.check_constraints cc ON tc.constraint_name = cc.constraint_name
WHERE tc.table_schema = 'public'
  AND tc.constraint_type = 'CHECK'
  AND tc.table_name IN ('profiles', 'doctors', 'patients', 'appointments', 'rooms', 'medical_records', 'prescriptions')
  AND cc.check_clause LIKE '%IN (%'
ORDER BY tc.table_name, tc.constraint_name;

-- =====================================================
-- 4. CHECK ENUM FUNCTIONS
-- =====================================================

SELECT 
  '=== ENUM FUNCTIONS ===' as section,
  '' as function_name,
  '' as status,
  '' as recommendation;

SELECT 
  'Functions' as section,
  routine_name as function_name,
  'EXISTS' as status,
  'Function is ready' as recommendation
FROM information_schema.routines 
WHERE routine_schema = 'public'
  AND routine_name IN ('get_enum_display_name', 'validate_enum_value', 'get_default_enum_value', 'get_enums_by_category')

UNION ALL

SELECT 
  'Functions' as section,
  missing_function as function_name,
  'MISSING' as status,
  'Run check-and-update-existing-database.sql' as recommendation
FROM (
  SELECT 'get_enum_display_name' as missing_function
  WHERE NOT EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'get_enum_display_name')
  
  UNION ALL
  
  SELECT 'validate_enum_value' as missing_function
  WHERE NOT EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'validate_enum_value')
  
  UNION ALL
  
  SELECT 'get_default_enum_value' as missing_function
  WHERE NOT EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'get_default_enum_value')
  
  UNION ALL
  
  SELECT 'get_enums_by_category' as missing_function
  WHERE NOT EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'get_enums_by_category')
) missing;

-- =====================================================
-- 5. CHECK ENUM DATA
-- =====================================================

SELECT 
  '=== ENUM DATA STATUS ===' as section,
  '' as category,
  '' as count,
  '' as recommendation;

-- Check if enum tables exist and have data
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'enum_categories') THEN
    -- Check enum categories
    PERFORM 1;
  END IF;
END $$;

-- If enum tables exist, show data status
SELECT 
  'Enum Categories' as section,
  category_name as category,
  COUNT(*)::TEXT as count,
  CASE 
    WHEN COUNT(*) = 0 THEN 'Run populate-enum-data.sql'
    ELSE 'Data exists'
  END as recommendation
FROM enum_categories
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'enum_categories')
GROUP BY category_name

UNION ALL

SELECT 
  'System Enums' as section,
  category_id as category,
  COUNT(*)::TEXT as count,
  CASE 
    WHEN COUNT(*) = 0 THEN 'Run populate-enum-data.sql'
    ELSE 'Data exists'
  END as recommendation
FROM system_enums
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'system_enums')
GROUP BY category_id
ORDER BY category;

-- =====================================================
-- 6. OVERALL STATUS SUMMARY
-- =====================================================

SELECT 
  '=== OVERALL STATUS ===' as section,
  '' as component,
  '' as status,
  '' as next_action;

-- Overall status check
WITH status_check AS (
  SELECT 
    CASE 
      WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'enum_categories')
       AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'system_enums')
      THEN 'TABLES_READY'
      ELSE 'TABLES_MISSING'
    END as tables_status,
    
    CASE 
      WHEN EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'validate_enum_value')
       AND EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'get_enum_display_name')
      THEN 'FUNCTIONS_READY'
      ELSE 'FUNCTIONS_MISSING'
    END as functions_status,
    
    CASE 
      WHEN EXISTS (SELECT 1 FROM enum_categories WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'enum_categories'))
       AND EXISTS (SELECT 1 FROM system_enums WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'system_enums'))
      THEN 'DATA_EXISTS'
      ELSE 'DATA_MISSING'
    END as data_status
)
SELECT 
  'Summary' as section,
  'Enum System' as component,
  CASE 
    WHEN tables_status = 'TABLES_READY' AND functions_status = 'FUNCTIONS_READY' AND data_status = 'DATA_EXISTS'
    THEN 'FULLY_READY'
    WHEN tables_status = 'TABLES_READY' AND functions_status = 'FUNCTIONS_READY'
    THEN 'READY_FOR_DATA'
    WHEN tables_status = 'TABLES_READY'
    THEN 'NEEDS_FUNCTIONS'
    ELSE 'NEEDS_SETUP'
  END as status,
  CASE 
    WHEN tables_status = 'TABLES_READY' AND functions_status = 'FUNCTIONS_READY' AND data_status = 'DATA_EXISTS'
    THEN 'System ready! Run migration script to remove CHECK constraints'
    WHEN tables_status = 'TABLES_READY' AND functions_status = 'FUNCTIONS_READY'
    THEN 'Run populate-enum-data.sql'
    WHEN tables_status = 'TABLES_READY'
    THEN 'Run check-and-update-existing-database.sql'
    ELSE 'Run check-and-update-existing-database.sql first'
  END as next_action
FROM status_check;

-- =====================================================
-- 7. RECOMMENDED COMMANDS
-- =====================================================

SELECT 
  '=== RECOMMENDED COMMANDS ===' as section,
  '' as step,
  '' as command,
  '' as description;

SELECT 
  'Commands' as section,
  '1' as step,
  'check-and-update-existing-database.sql' as command,
  'Create enum tables and functions' as description

UNION ALL

SELECT 
  'Commands' as section,
  '2' as step,
  'populate-enum-data.sql' as command,
  'Add enum categories and values' as description

UNION ALL

SELECT 
  'Commands' as section,
  '3' as step,
  'migrate-to-dynamic-enums.sql' as command,
  'Remove CHECK constraints and add validation triggers' as description

UNION ALL

SELECT 
  'Commands' as section,
  '4' as step,
  'Test enum functions' as command,
  'SELECT validate_enum_value(''ROLE'', ''admin'');' as description

ORDER BY step;
