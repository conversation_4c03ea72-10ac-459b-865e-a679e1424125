const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// <PERSON><PERSON>u sắc cho console
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function listAuthUsers(supabase) {
  log('blue', '📋 Lấy danh sách users từ auth.users...');

  try {
    const { data, error } = await supabase.auth.admin.listUsers();

    if (error) {
      log('red', `❌ Lỗi khi lấy auth users: ${error.message}`);
      return null;
    }

    return data.users;
  } catch (error) {
    log('red', `❌ Exception khi lấy auth users: ${error.message}`);
    return null;
  }
}

async function deleteAuthUserById(supabase, userId, email) {
  try {
    log('yellow', `🗑️  Đang xóa user: ${email} (${userId})`);

    const { error } = await supabase.auth.admin.deleteUser(userId);

    if (error) {
      log('red', `❌ Lỗi xóa ${email}: ${error.message}`);
      return false;
    }

    log('green', `✅ Đã xóa thành công: ${email}`);
    return true;
  } catch (error) {
    log('red', `❌ Exception xóa ${email}: ${error.message}`);
    return false;
  }
}

async function manualDeleteUsers() {
  log('cyan', '🗑️  FORCE DELETE AUTH USERS - DIRECT METHOD');

  try {
    // Tạo client với service role
    const supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Thử lấy danh sách auth users trực tiếp
    const authUsers = await listAuthUsers(supabase);

    if (!authUsers) {
      log('red', '❌ Không thể truy cập auth.users');
      log('yellow', '💡 Hãy thử các phương pháp sau:');
      log('yellow', '1. Xóa qua Supabase Dashboard');
      log('yellow', '2. Kiểm tra Service Role Key');
      log('yellow', '3. Kiểm tra project settings');
      return;
    }

    if (authUsers.length === 0) {
      log('green', '✅ Không có auth users nào cần xóa');
      return;
    }

    log('green', `✅ Tìm thấy ${authUsers.length} auth users:`);
    authUsers.forEach((user, index) => {
      const createdAt = new Date(user.created_at).toLocaleString('vi-VN');
      log('blue', `${index + 1}. ${user.email || 'No email'} - ID: ${user.id.substring(0, 8)}...`);
      log('blue', `   📅 Tạo: ${createdAt}`);
    });

    // Hỏi người dùng có muốn xóa không
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    rl.question('\n❓ Bạn có muốn xóa TẤT CẢ auth users? (y/N): ', async (answer) => {
      if (answer.toLowerCase() === 'y') {
        log('red', '⚠️  BẮT ĐẦU XÓA AUTH USERS...');

        let successCount = 0;
        let failCount = 0;

        for (const user of authUsers) {
          const success = await deleteAuthUserById(supabase, user.id, user.email || 'No email');
          if (success) {
            successCount++;
          } else {
            failCount++;
          }

          // Delay nhỏ để tránh rate limit
          await new Promise(resolve => setTimeout(resolve, 100));
        }

        log('cyan', `\n📊 KẾT QUẢ:`);
        log('green', `✅ Thành công: ${successCount}`);
        log('red', `❌ Thất bại: ${failCount}`);

        if (failCount > 0) {
          log('yellow', '\n💡 NẾU VẪN CÒN USERS:');
          log('yellow', '1. Thử xóa qua Supabase Dashboard');
          log('yellow', '2. Hoặc dùng SQL Editor:');
          console.log('   DELETE FROM auth.users;');
        }

      } else {
        log('blue', 'Đã hủy thao tác');

        log('yellow', '\n📝 HƯỚNG DẪN XÓA THỦ CÔNG:');
        log('yellow', '1. Vào: https://app.supabase.com');
        log('yellow', '2. Chọn project của bạn');
        log('yellow', '3. Authentication → Users');
        log('yellow', '4. Xóa từng user hoặc dùng SQL Editor:');
        console.log('\n-- Xóa tất cả users:');
        console.log('DELETE FROM auth.users;');
        console.log('\n-- Xóa user cụ thể:');
        console.log('DELETE FROM auth.users WHERE email = \'<EMAIL>\';');
      }

      rl.close();
    });

  } catch (error) {
    log('red', `❌ Lỗi: ${error.message}`);
  }
}

manualDeleteUsers();
