-- =====================================================
-- FIX SIGNUP RLS POLICIES
-- =====================================================
-- This script fixes the "Database error saving new user" issue
-- by updating RLS policies and trigger function

-- 1. Add INSERT policy for profiles table to allow signup
DROP POLICY IF EXISTS "Allow profile creation during signup" ON profiles;
CREATE POLICY "Allow profile creation during signup" ON profiles
  FOR INSERT WITH CHECK (true);

-- 2. Update the trigger function to use metadata from signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (
    id, 
    email, 
    full_name, 
    phone_number,
    role,
    email_verified,
    is_active
  )
  VALUES (
    NEW.id, 
    NEW.email, 
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email), 
    NEW.raw_user_meta_data->>'phone_number',
    COALESCE(NEW.raw_user_meta_data->>'role', 'patient'),
    CASE WHEN NEW.email_confirmed_at IS NOT NULL THEN true ELSE false END,
    true
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Ensure trigger is properly set up
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 4. Add INSERT policies for doctors and patients tables
DROP POLICY IF EXISTS "Allow doctor profile creation" ON doctors;
CREATE POLICY "Allow doctor profile creation" ON doctors
  FOR INSERT WITH CHECK (true);

DROP POLICY IF EXISTS "Allow patient profile creation" ON patients;
CREATE POLICY "Allow patient profile creation" ON patients
  FOR INSERT WITH CHECK (true);

-- 5. Verify policies are in place
SELECT 'RLS policies updated successfully for signup!' as status;

-- Show current policies for verification
SELECT 
  schemaname,
  tablename,
  policyname,
  cmd,
  qual,
  with_check
FROM pg_policies 
WHERE tablename IN ('profiles', 'doctors', 'patients')
ORDER BY tablename, policyname;
