const { createClient } = require('@supabase/supabase-js');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Required: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixSignupIssue() {
  console.log('🔧 Analyzing signup issue...');

  try {
    // Test basic connection
    console.log('🔍 Testing Supabase connection...');
    const { data: testData, error: testError } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);

    if (testError) {
      console.error('❌ Connection test failed:', testError);
      return;
    }

    console.log('✅ Supabase connection working');

    // Check current policies
    console.log('🔍 Checking current RLS policies...');
    const { data: policies, error: policiesError } = await supabase
      .from('pg_policies')
      .select('*')
      .eq('tablename', 'profiles');

    if (policiesError) {
      console.error('❌ Error checking policies:', policiesError);
    } else {
      console.log('📋 Current policies for profiles table:');
      policies.forEach(policy => {
        console.log(`  - ${policy.policyname}: ${policy.cmd}`);
      });
    }

    // Test signup with a simple case
    console.log('🧪 Testing signup process...');
    const testEmail = `test-${Date.now()}@example.com`;
    const testPassword = 'TestPassword123';

    const { data: signupData, error: signupError } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          full_name: 'Test User',
          role: 'patient'
        }
      }
    });

    if (signupError) {
      console.error('❌ Signup test failed:', signupError);
    } else {
      console.log('✅ Signup test successful:', signupData.user?.id);

      // Check if profile was created
      if (signupData.user) {
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', signupData.user.id)
          .single();

        if (profileError) {
          console.error('❌ Profile not created:', profileError);
        } else {
          console.log('✅ Profile created successfully:', profileData);
        }

        // Clean up test user
        const { error: deleteError } = await supabase.auth.admin.deleteUser(signupData.user.id);
        if (deleteError) {
          console.warn('⚠️ Could not delete test user:', deleteError);
        } else {
          console.log('🧹 Test user cleaned up');
        }
      }
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

fixSignupIssue();
