-- =====================================================
-- CREATE SIMPLE VIEWS SCRIPT
-- =====================================================
-- This script creates useful views for better data access
-- Run this AFTER fix-missing-columns.sql

-- =====================================================
-- 1. CREATE VIEWS FOR BETTER DATA ACCESS
-- =====================================================

-- View for complete doctor information
CREATE OR REPLACE VIEW v_doctors_complete AS
SELECT 
  d.*,
  p.full_name,
  p.email,
  p.phone_number,
  p.avatar_url,
  p.is_active as profile_active,
  dept.name as department_name,
  dept.location as department_location
FROM doctors d
JOIN profiles p ON d.profile_id = p.id
LEFT JOIN departments dept ON d.department_id = dept.department_id;

-- View for complete patient information
CREATE OR REPLACE VIEW v_patients_complete AS
SELECT 
  pt.*,
  p.full_name,
  p.email,
  p.phone_number,
  p.avatar_url,
  p.is_active as profile_active
FROM patients pt
JOIN profiles p ON pt.profile_id = p.id;

-- View for appointment details
CREATE OR REPLACE VIEW v_appointments_detailed AS
SELECT 
  a.*,
  p_patient.full_name as patient_name,
  p_patient.phone_number as patient_phone,
  p_doctor.full_name as doctor_name,
  d.specialization as doctor_specialization,
  dept.name as department_name,
  r.room_number,
  r.location as room_location
FROM appointments a
JOIN patients pt ON a.patient_id = pt.patient_id
JOIN profiles p_patient ON pt.profile_id = p_patient.id
JOIN doctors d ON a.doctor_id = d.doctor_id
JOIN profiles p_doctor ON d.profile_id = p_doctor.id
LEFT JOIN departments dept ON d.department_id = dept.department_id
LEFT JOIN rooms r ON a.room_id = r.room_id;

-- View for enum options (useful for frontend)
CREATE OR REPLACE VIEW v_enum_options AS
SELECT 
  se.category_id,
  ec.display_name_vi as category_name,
  se.enum_key,
  se.display_name_vi,
  se.display_name_en,
  se.description_vi,
  se.description_en,
  se.sort_order,
  se.color_code,
  se.icon_name,
  se.is_default
FROM system_enums se
JOIN enum_categories ec ON se.category_id = ec.category_id
WHERE se.is_active = true AND ec.is_active = true
ORDER BY se.category_id, se.sort_order, se.display_name_vi;

-- View for departments with head doctor info
CREATE OR REPLACE VIEW v_departments_with_head AS
SELECT 
  dept.*,
  d.doctor_id as head_doctor_id,
  p.full_name as head_doctor_name,
  d.specialization as head_doctor_specialization
FROM departments dept
LEFT JOIN doctors d ON dept.head_doctor_id = d.doctor_id
LEFT JOIN profiles p ON d.profile_id = p.id;

-- View for rooms with department info
CREATE OR REPLACE VIEW v_rooms_with_department AS
SELECT 
  r.*,
  dept.name as department_name,
  dept.location as department_location
FROM rooms r
JOIN departments dept ON r.department_id = dept.department_id;

-- View for medical records with patient and doctor info
CREATE OR REPLACE VIEW v_medical_records_detailed AS
SELECT 
  mr.*,
  p_patient.full_name as patient_name,
  p_patient.phone_number as patient_phone,
  p_doctor.full_name as doctor_name,
  d.specialization as doctor_specialization,
  dept.name as department_name
FROM medical_records mr
JOIN patients pt ON mr.patient_id = pt.patient_id
JOIN profiles p_patient ON pt.profile_id = p_patient.id
JOIN doctors d ON mr.doctor_id = d.doctor_id
JOIN profiles p_doctor ON d.profile_id = p_doctor.id
LEFT JOIN departments dept ON d.department_id = dept.department_id;

-- View for prescriptions with patient and doctor info
CREATE OR REPLACE VIEW v_prescriptions_detailed AS
SELECT 
  pr.*,
  p_patient.full_name as patient_name,
  p_patient.phone_number as patient_phone,
  p_doctor.full_name as doctor_name,
  d.specialization as doctor_specialization
FROM prescriptions pr
JOIN patients pt ON pr.patient_id = pt.patient_id
JOIN profiles p_patient ON pt.profile_id = p_patient.id
JOIN doctors d ON pr.doctor_id = d.doctor_id
JOIN profiles p_doctor ON d.profile_id = p_doctor.id;

-- =====================================================
-- 2. CREATE SIMPLE UTILITY VIEWS
-- =====================================================

-- View for active doctors by department
CREATE OR REPLACE VIEW v_active_doctors_by_department AS
SELECT 
  dept.department_id,
  dept.name as department_name,
  COUNT(d.doctor_id) as doctor_count,
  string_agg(p.full_name, ', ') as doctor_names
FROM departments dept
LEFT JOIN doctors d ON dept.department_id = d.department_id AND d.status = 'active'
LEFT JOIN profiles p ON d.profile_id = p.id
WHERE dept.is_active = true
GROUP BY dept.department_id, dept.name
ORDER BY dept.name;

-- View for appointment statistics by doctor
CREATE OR REPLACE VIEW v_doctor_appointment_stats AS
SELECT 
  d.doctor_id,
  p.full_name as doctor_name,
  d.specialization,
  COUNT(a.appointment_id) as total_appointments,
  COUNT(CASE WHEN a.status = 'completed' THEN 1 END) as completed_appointments,
  COUNT(CASE WHEN a.status = 'scheduled' THEN 1 END) as scheduled_appointments,
  COUNT(CASE WHEN a.status = 'cancelled' THEN 1 END) as cancelled_appointments
FROM doctors d
JOIN profiles p ON d.profile_id = p.id
LEFT JOIN appointments a ON d.doctor_id = a.doctor_id
WHERE d.status = 'active'
GROUP BY d.doctor_id, p.full_name, d.specialization
ORDER BY total_appointments DESC;

-- View for room utilization
CREATE OR REPLACE VIEW v_room_utilization AS
SELECT 
  r.room_id,
  r.room_number,
  r.room_type,
  dept.name as department_name,
  r.status,
  COUNT(a.appointment_id) as total_bookings,
  COUNT(CASE WHEN a.status = 'completed' THEN 1 END) as completed_bookings
FROM rooms r
JOIN departments dept ON r.department_id = dept.department_id
LEFT JOIN appointments a ON r.room_id = a.room_id
GROUP BY r.room_id, r.room_number, r.room_type, dept.name, r.status
ORDER BY total_bookings DESC;

-- =====================================================
-- 3. VERIFY VIEWS CREATED
-- =====================================================

-- Check if all views were created successfully
SELECT 
  'Views Created' as check_type,
  table_name as view_name,
  '✅ SUCCESS' as status
FROM information_schema.views 
WHERE table_schema = 'public' 
  AND table_name LIKE 'v_%'
ORDER BY table_name;

-- =====================================================
-- 4. SUCCESS MESSAGE
-- =====================================================

SELECT 
  '✅ VIEWS CREATED SUCCESSFULLY!' as status,
  'All database views are now available' as message,
  'You can now query complex data easily' as benefit;
