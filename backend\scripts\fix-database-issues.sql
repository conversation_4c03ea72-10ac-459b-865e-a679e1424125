-- =====================================================
-- FIX DATABASE ISSUES SCRIPT
-- =====================================================
-- This script fixes critical database structure issues
-- Run this in Supabase SQL Editor

-- =====================================================
-- 1. ADD MISSING FOREIGN KEY CONSTRAINTS
-- =====================================================

-- Add FK from doctors.department_id to departments.department_id
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints
    WHERE constraint_name = 'doctors_department_id_fkey'
  ) THEN
    ALTER TABLE doctors
    ADD CONSTRAINT doctors_department_id_fkey
    FOREIGN KEY (department_id) REFERENCES departments(department_id);

    RAISE NOTICE '✅ Added FK: doctors.department_id -> departments.department_id';
  ELSE
    RAISE NOTICE '⚠️ FK already exists: doctors.department_id -> departments.department_id';
  END IF;
END $$;

-- Add FK from appointments.room_id to rooms.room_id
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints
    WHERE constraint_name = 'appointments_room_id_fkey'
  ) THEN
    ALTER TABLE appointments
    ADD CONSTRAINT appointments_room_id_fkey
    FOREIGN KEY (room_id) REFERENCES rooms(room_id);

    RAISE NOTICE '✅ Added FK: appointments.room_id -> rooms.room_id';
  ELSE
    RAISE NOTICE '⚠️ FK already exists: appointments.room_id -> rooms.room_id';
  END IF;
END $$;

-- =====================================================
-- 2. ADD MISSING INDEXES FOR PERFORMANCE
-- =====================================================

-- Index for doctors.department_id
CREATE INDEX IF NOT EXISTS idx_doctors_department_id ON doctors(department_id);

-- Index for appointments.room_id
CREATE INDEX IF NOT EXISTS idx_appointments_room_id ON appointments(room_id);

-- Index for medical_records.appointment_id
CREATE INDEX IF NOT EXISTS idx_medical_records_appointment_id ON medical_records(appointment_id);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_appointments_doctor_datetime ON appointments(doctor_id, appointment_datetime);
CREATE INDEX IF NOT EXISTS idx_appointments_patient_datetime ON appointments(patient_id, appointment_datetime);

RAISE NOTICE '✅ Added performance indexes';

-- =====================================================
-- 3. ADD DATA VALIDATION CONSTRAINTS
-- =====================================================

-- Ensure appointment datetime is not in the past (allow 1 hour buffer)
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.check_constraints
    WHERE constraint_name = 'appointments_datetime_future'
  ) THEN
    ALTER TABLE appointments
    ADD CONSTRAINT appointments_datetime_future
    CHECK (appointment_datetime > NOW() - INTERVAL '1 hour');

    RAISE NOTICE '✅ Added constraint: appointments must be in future';
  END IF;
END $$;

-- Ensure patient birth date is reasonable
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.check_constraints
    WHERE constraint_name = 'patients_birth_date_valid'
  ) THEN
    ALTER TABLE patients
    ADD CONSTRAINT patients_birth_date_valid
    CHECK (date_of_birth <= CURRENT_DATE AND date_of_birth >= '1900-01-01');

    RAISE NOTICE '✅ Added constraint: valid birth date range';
  END IF;
END $$;

-- Ensure room capacity is positive
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.check_constraints
    WHERE constraint_name = 'rooms_capacity_positive'
  ) THEN
    ALTER TABLE rooms
    ADD CONSTRAINT rooms_capacity_positive
    CHECK (capacity > 0);

    RAISE NOTICE '✅ Added constraint: room capacity must be positive';
  END IF;
END $$;

-- =====================================================
-- 4. CREATE ENUM VALIDATION FUNCTION
-- =====================================================

CREATE OR REPLACE FUNCTION validate_enum_value(
  p_category_id TEXT,
  p_enum_key TEXT
) RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM system_enums
    WHERE category_id = p_category_id
      AND enum_key = p_enum_key
      AND is_active = true
  );
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 5. CREATE ENUM VALIDATION TRIGGERS
-- =====================================================

-- Function to validate enum fields
CREATE OR REPLACE FUNCTION validate_enum_fields()
RETURNS TRIGGER AS $$
BEGIN
  -- Validate profiles.role
  IF TG_TABLE_NAME = 'profiles' AND NEW.role IS NOT NULL THEN
    IF NOT validate_enum_value('ROLE', NEW.role) THEN
      RAISE EXCEPTION 'Invalid role: %. Valid values: %',
        NEW.role,
        (SELECT string_agg(enum_key, ', ') FROM system_enums WHERE category_id = 'ROLE' AND is_active = true);
    END IF;
  END IF;

  -- Validate doctors.status
  IF TG_TABLE_NAME = 'doctors' AND NEW.status IS NOT NULL THEN
    IF NOT validate_enum_value('DOCTOR_STATUS', NEW.status) THEN
      RAISE EXCEPTION 'Invalid doctor status: %. Valid values: %',
        NEW.status,
        (SELECT string_agg(enum_key, ', ') FROM system_enums WHERE category_id = 'DOCTOR_STATUS' AND is_active = true);
    END IF;
  END IF;

  -- Validate patients.gender
  IF TG_TABLE_NAME = 'patients' AND NEW.gender IS NOT NULL THEN
    IF NOT validate_enum_value('GENDER', NEW.gender) THEN
      RAISE EXCEPTION 'Invalid gender: %. Valid values: %',
        NEW.gender,
        (SELECT string_agg(enum_key, ', ') FROM system_enums WHERE category_id = 'GENDER' AND is_active = true);
    END IF;
  END IF;

  -- Validate patients.blood_type
  IF TG_TABLE_NAME = 'patients' AND NEW.blood_type IS NOT NULL THEN
    IF NOT validate_enum_value('BLOOD_TYPE', NEW.blood_type) THEN
      RAISE EXCEPTION 'Invalid blood type: %. Valid values: %',
        NEW.blood_type,
        (SELECT string_agg(enum_key, ', ') FROM system_enums WHERE category_id = 'BLOOD_TYPE' AND is_active = true);
    END IF;
  END IF;

  -- Validate patients.status
  IF TG_TABLE_NAME = 'patients' AND NEW.status IS NOT NULL THEN
    IF NOT validate_enum_value('PATIENT_STATUS', NEW.status) THEN
      RAISE EXCEPTION 'Invalid patient status: %. Valid values: %',
        NEW.status,
        (SELECT string_agg(enum_key, ', ') FROM system_enums WHERE category_id = 'PATIENT_STATUS' AND is_active = true);
    END IF;
  END IF;

  -- Validate appointments.type
  IF TG_TABLE_NAME = 'appointments' AND NEW.type IS NOT NULL THEN
    IF NOT validate_enum_value('APPOINTMENT_TYPE', NEW.type) THEN
      RAISE EXCEPTION 'Invalid appointment type: %. Valid values: %',
        NEW.type,
        (SELECT string_agg(enum_key, ', ') FROM system_enums WHERE category_id = 'APPOINTMENT_TYPE' AND is_active = true);
    END IF;
  END IF;

  -- Validate appointments.status
  IF TG_TABLE_NAME = 'appointments' AND NEW.status IS NOT NULL THEN
    IF NOT validate_enum_value('APPOINTMENT_STATUS', NEW.status) THEN
      RAISE EXCEPTION 'Invalid appointment status: %. Valid values: %',
        NEW.status,
        (SELECT string_agg(enum_key, ', ') FROM system_enums WHERE category_id = 'APPOINTMENT_STATUS' AND is_active = true);
    END IF;
  END IF;

  -- Validate rooms.room_type
  IF TG_TABLE_NAME = 'rooms' AND NEW.room_type IS NOT NULL THEN
    IF NOT validate_enum_value('ROOM_TYPE', NEW.room_type) THEN
      RAISE EXCEPTION 'Invalid room type: %. Valid values: %',
        NEW.room_type,
        (SELECT string_agg(enum_key, ', ') FROM system_enums WHERE category_id = 'ROOM_TYPE' AND is_active = true);
    END IF;
  END IF;

  -- Validate rooms.status
  IF TG_TABLE_NAME = 'rooms' AND NEW.status IS NOT NULL THEN
    IF NOT validate_enum_value('ROOM_STATUS', NEW.status) THEN
      RAISE EXCEPTION 'Invalid room status: %. Valid values: %',
        NEW.status,
        (SELECT string_agg(enum_key, ', ') FROM system_enums WHERE category_id = 'ROOM_STATUS' AND is_active = true);
    END IF;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for enum validation
DROP TRIGGER IF EXISTS validate_profiles_enum ON profiles;
CREATE TRIGGER validate_profiles_enum
  BEFORE INSERT OR UPDATE ON profiles
  FOR EACH ROW EXECUTE FUNCTION validate_enum_fields();

DROP TRIGGER IF EXISTS validate_doctors_enum ON doctors;
CREATE TRIGGER validate_doctors_enum
  BEFORE INSERT OR UPDATE ON doctors
  FOR EACH ROW EXECUTE FUNCTION validate_enum_fields();

DROP TRIGGER IF EXISTS validate_patients_enum ON patients;
CREATE TRIGGER validate_patients_enum
  BEFORE INSERT OR UPDATE ON patients
  FOR EACH ROW EXECUTE FUNCTION validate_enum_fields();

DROP TRIGGER IF EXISTS validate_appointments_enum ON appointments;
CREATE TRIGGER validate_appointments_enum
  BEFORE INSERT OR UPDATE ON appointments
  FOR EACH ROW EXECUTE FUNCTION validate_enum_fields();

DROP TRIGGER IF EXISTS validate_rooms_enum ON rooms;
CREATE TRIGGER validate_rooms_enum
  BEFORE INSERT OR UPDATE ON rooms
  FOR EACH ROW EXECUTE FUNCTION validate_enum_fields();

-- =====================================================
-- 6. IMPROVE ID GENERATION
-- =====================================================

-- Function for better ID generation with sequence
CREATE OR REPLACE FUNCTION generate_unique_id(prefix TEXT)
RETURNS TEXT AS $$
DECLARE
  new_id TEXT;
  counter INTEGER := 0;
BEGIN
  LOOP
    new_id := prefix || EXTRACT(EPOCH FROM NOW())::BIGINT || LPAD(counter::TEXT, 3, '0');

    -- Check if ID already exists in any table (simplified check)
    IF NOT EXISTS (
      SELECT 1 FROM doctors WHERE doctor_id = new_id
      UNION ALL
      SELECT 1 FROM patients WHERE patient_id = new_id
      UNION ALL
      SELECT 1 FROM appointments WHERE appointment_id = new_id
    ) THEN
      RETURN new_id;
    END IF;

    counter := counter + 1;
    IF counter > 999 THEN
      RAISE EXCEPTION 'Unable to generate unique ID after 1000 attempts';
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 7. IMPROVE RLS POLICIES
-- =====================================================

-- Add missing INSERT policies for patients
DROP POLICY IF EXISTS "Patients can create own profile" ON patients;
CREATE POLICY "Patients can create own profile" ON patients
  FOR INSERT WITH CHECK (profile_id = auth.uid());

-- Add missing INSERT policies for doctors
DROP POLICY IF EXISTS "Allow doctor profile creation" ON doctors;
CREATE POLICY "Allow doctor profile creation" ON doctors
  FOR INSERT WITH CHECK (true); -- Admin will handle this via application logic

-- Improve appointments policies
DROP POLICY IF EXISTS "Patients can create appointments" ON appointments;
CREATE POLICY "Patients can create appointments" ON appointments
  FOR INSERT WITH CHECK (
    patient_id IN (
      SELECT patient_id FROM patients WHERE profile_id = auth.uid()
    )
  );

DROP POLICY IF EXISTS "Doctors can create appointments" ON appointments;
CREATE POLICY "Doctors can create appointments" ON appointments
  FOR INSERT WITH CHECK (
    doctor_id IN (
      SELECT doctor_id FROM doctors WHERE profile_id = auth.uid()
    )
  );

-- =====================================================
-- 8. CREATE VIEWS FOR BETTER DATA ACCESS
-- =====================================================

-- View for complete doctor information
CREATE OR REPLACE VIEW v_doctors_complete AS
SELECT
  d.*,
  p.full_name,
  p.email,
  p.phone_number,
  p.avatar_url,
  p.is_active as profile_active,
  dept.name as department_name,
  dept.location as department_location
FROM doctors d
JOIN profiles p ON d.profile_id = p.id
LEFT JOIN departments dept ON d.department_id = dept.department_id;

-- View for complete patient information
CREATE OR REPLACE VIEW v_patients_complete AS
SELECT
  pt.*,
  p.full_name,
  p.email,
  p.phone_number,
  p.avatar_url,
  p.is_active as profile_active
FROM patients pt
JOIN profiles p ON pt.profile_id = p.id;

-- View for appointment details
CREATE OR REPLACE VIEW v_appointments_detailed AS
SELECT
  a.*,
  p_patient.full_name as patient_name,
  p_patient.phone_number as patient_phone,
  p_doctor.full_name as doctor_name,
  d.specialization as doctor_specialization,
  dept.name as department_name,
  r.room_number,
  r.location as room_location
FROM appointments a
JOIN patients pt ON a.patient_id = pt.patient_id
JOIN profiles p_patient ON pt.profile_id = p_patient.id
JOIN doctors d ON a.doctor_id = d.doctor_id
JOIN profiles p_doctor ON d.profile_id = p_doctor.id
LEFT JOIN departments dept ON d.department_id = dept.department_id
LEFT JOIN rooms r ON a.room_id = r.room_id;

-- =====================================================
-- 9. CREATE USEFUL FUNCTIONS
-- =====================================================

-- Function to get available time slots for a doctor
CREATE OR REPLACE FUNCTION get_available_slots(
  p_doctor_id TEXT,
  p_date DATE,
  p_duration_minutes INTEGER DEFAULT 30
)
RETURNS TABLE(
  slot_time TIMESTAMPTZ,
  is_available BOOLEAN
) AS $$
DECLARE
  start_hour INTEGER := 8;  -- 8 AM
  end_hour INTEGER := 17;   -- 5 PM
  slot_start TIMESTAMPTZ;
  slot_end TIMESTAMPTZ;
BEGIN
  FOR hour_offset IN 0..(end_hour - start_hour) LOOP
    FOR minute_offset IN 0..1 LOOP -- 30-minute slots
      slot_start := p_date + (start_hour + hour_offset || ' hours')::INTERVAL + (minute_offset * 30 || ' minutes')::INTERVAL;
      slot_end := slot_start + (p_duration_minutes || ' minutes')::INTERVAL;

      -- Check if slot is available
      slot_time := slot_start;
      is_available := NOT EXISTS (
        SELECT 1 FROM appointments
        WHERE doctor_id = p_doctor_id
          AND appointment_datetime < slot_end
          AND appointment_datetime + (duration_minutes || ' minutes')::INTERVAL > slot_start
          AND status NOT IN ('cancelled', 'completed')
      );

      RETURN NEXT;
    END LOOP;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to get doctor statistics
CREATE OR REPLACE FUNCTION get_doctor_stats(p_doctor_id TEXT)
RETURNS TABLE(
  total_appointments INTEGER,
  completed_appointments INTEGER,
  cancelled_appointments INTEGER,
  average_rating DECIMAL,
  total_patients INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    COUNT(*)::INTEGER as total_appointments,
    COUNT(CASE WHEN status = 'completed' THEN 1 END)::INTEGER as completed_appointments,
    COUNT(CASE WHEN status = 'cancelled' THEN 1 END)::INTEGER as cancelled_appointments,
    0.0::DECIMAL as average_rating, -- Placeholder for future rating system
    COUNT(DISTINCT patient_id)::INTEGER as total_patients
  FROM appointments
  WHERE doctor_id = p_doctor_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 10. SUCCESS MESSAGE
-- =====================================================

SELECT
  '✅ DATABASE FIXES COMPLETED SUCCESSFULLY!' as status,
  'All critical issues have been addressed' as message,
  'Your database is now more robust and secure' as result;
