-- =====================================================
-- XÓA AUTH USERS - SUPABASE SQL SCRIPT
-- =====================================================
-- Sử dụng script này trong Supabase SQL Editor
-- Đường dẫn: Dashboard → SQL Editor → New Query

-- =====================================================
-- BƯỚC 1: XEM DANH SÁCH USERS HIỆN TẠI
-- =====================================================
-- Chạy câu lệnh này trước để xem có users nào:

SELECT 
  id,
  email,
  created_at,
  last_sign_in_at,
  email_confirmed_at
FROM auth.users
ORDER BY created_at DESC;

-- =====================================================
-- BƯỚC 2: XÓA USER CỤ THỂ (AN TOÀN HỚN)
-- =====================================================
-- Thay '<EMAIL>' bằng email thực tế:

-- DELETE FROM auth.users WHERE email = '<EMAIL>';

-- Hoặc xóa theo ID:
-- DELETE FROM auth.users WHERE id = 'user-id-here';

-- =====================================================
-- BƯỚC 3: XÓA TẤT CẢ USERS (NGUY HIỂM!)
-- =====================================================
-- CHỈ CHẠY NẾU BẠN CHẮC CHẮN MUỐN XÓA TẤT CẢ!
-- Bỏ comment (xóa --) ở dòng dưới để chạy:

-- DELETE FROM auth.users;

-- =====================================================
-- BƯỚC 4: KIỂM TRA KẾT QUẢ
-- =====================================================
-- Chạy lại để kiểm tra:

SELECT COUNT(*) as total_users FROM auth.users;

-- =====================================================
-- LƯU Ý QUAN TRỌNG
-- =====================================================
/*
1. BACKUP TRƯỚC KHI XÓA:
   - Xuất dữ liệu nếu cần thiết
   - Thao tác này KHÔNG THỂ HOÀN TÁC

2. THỨ TỰ XÓA AN TOÀN:
   - Xóa dữ liệu phụ thuộc trước (đã làm)
   - Xóa profiles (đã làm)
   - Cuối cùng xóa auth.users (bước này)

3. SAU KHI XÓA:
   - Tất cả sessions sẽ bị hủy
   - Users sẽ không thể đăng nhập
   - Cần tạo users mới nếu muốn test

4. TẠI SAO SCRIPT NODE.JS KHÔNG HOẠT ĐỘNG:
   - Có thể do cấu hình Supabase project
   - Hoặc Service Role Key không có đủ quyền
   - SQL Editor có quyền cao hơn
*/

-- =====================================================
-- HƯỚNG DẪN SỬ DỤNG
-- =====================================================
/*
1. Copy toàn bộ nội dung file này
2. Vào Supabase Dashboard
3. Chọn project của bạn
4. Vào SQL Editor
5. Tạo query mới
6. Paste nội dung này vào
7. Chạy từng phần một cách cẩn thận
*/
