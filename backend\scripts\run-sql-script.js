const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Required: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runSqlScript(scriptPath) {
  try {
    console.log(`📄 Reading SQL script: ${scriptPath}`);
    
    const fullPath = path.resolve(scriptPath);
    if (!fs.existsSync(fullPath)) {
      throw new Error(`Script file not found: ${fullPath}`);
    }
    
    const sqlContent = fs.readFileSync(fullPath, 'utf8');
    console.log(`📝 Script content length: ${sqlContent.length} characters`);
    
    console.log('🚀 Executing SQL script...');
    const { data, error } = await supabase.rpc('exec_sql', { sql: sqlContent });
    
    if (error) {
      console.error('❌ SQL execution error:', error);
      return false;
    }
    
    console.log('✅ SQL script executed successfully');
    if (data) {
      console.log('📊 Result:', data);
    }
    
    return true;
  } catch (error) {
    console.error('❌ Error running SQL script:', error.message);
    return false;
  }
}

// Get script path from command line arguments
const scriptPath = process.argv[2];
if (!scriptPath) {
  console.error('❌ Please provide a SQL script path');
  console.error('Usage: node run-sql-script.js <path-to-sql-file>');
  process.exit(1);
}

runSqlScript(scriptPath)
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('❌ Unexpected error:', error);
    process.exit(1);
  });
