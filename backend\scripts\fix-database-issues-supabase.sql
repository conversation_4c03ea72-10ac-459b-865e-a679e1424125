-- =====================================================
-- FIX DATABASE ISSUES SCRIPT - SUPABASE COMPATIBLE
-- =====================================================
-- This script fixes critical database structure issues
-- Compatible with Supabase SQL Editor

-- =====================================================
-- 1. ADD MISSING FOREIGN KEY CONSTRAINTS
-- =====================================================

-- Add FK from doctors.department_id to departments.department_id
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'doctors_department_id_fkey'
  ) THEN
    ALTER TABLE doctors 
    ADD CONSTRAINT doctors_department_id_fkey 
    FOREIGN KEY (department_id) REFERENCES departments(department_id);
  END IF;
END $$;

-- Add FK from appointments.room_id to rooms.room_id
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'appointments_room_id_fkey'
  ) THEN
    ALTER TABLE appointments 
    ADD CONSTRAINT appointments_room_id_fkey 
    FOREIGN KEY (room_id) REFERENCES rooms(room_id);
  END IF;
END $$;

-- =====================================================
-- 2. ADD MISSING INDEXES FOR PERFORMANCE
-- =====================================================

-- Index for doctors.department_id
CREATE INDEX IF NOT EXISTS idx_doctors_department_id ON doctors(department_id);

-- Index for appointments.room_id  
CREATE INDEX IF NOT EXISTS idx_appointments_room_id ON appointments(room_id);

-- Index for medical_records.appointment_id
CREATE INDEX IF NOT EXISTS idx_medical_records_appointment_id ON medical_records(appointment_id);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_appointments_doctor_datetime ON appointments(doctor_id, appointment_datetime);
CREATE INDEX IF NOT EXISTS idx_appointments_patient_datetime ON appointments(patient_id, appointment_datetime);

-- =====================================================
-- 3. ADD DATA VALIDATION CONSTRAINTS
-- =====================================================

-- Ensure appointment datetime is not in the past (allow 1 hour buffer)
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.check_constraints 
    WHERE constraint_name = 'appointments_datetime_future'
  ) THEN
    ALTER TABLE appointments 
    ADD CONSTRAINT appointments_datetime_future 
    CHECK (appointment_datetime > NOW() - INTERVAL '1 hour');
  END IF;
END $$;

-- Ensure patient birth date is reasonable
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.check_constraints 
    WHERE constraint_name = 'patients_birth_date_valid'
  ) THEN
    ALTER TABLE patients 
    ADD CONSTRAINT patients_birth_date_valid 
    CHECK (date_of_birth <= CURRENT_DATE AND date_of_birth >= '1900-01-01');
  END IF;
END $$;

-- Ensure room capacity is positive
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.check_constraints 
    WHERE constraint_name = 'rooms_capacity_positive'
  ) THEN
    ALTER TABLE rooms 
    ADD CONSTRAINT rooms_capacity_positive 
    CHECK (capacity > 0);
  END IF;
END $$;

-- =====================================================
-- 4. CREATE ENUM VALIDATION FUNCTION
-- =====================================================

CREATE OR REPLACE FUNCTION validate_enum_value(
  p_category_id TEXT,
  p_enum_key TEXT
) RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM system_enums 
    WHERE category_id = p_category_id 
      AND enum_key = p_enum_key 
      AND is_active = true
  );
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 5. CREATE ENUM VALIDATION TRIGGERS
-- =====================================================

-- Function to validate enum fields
CREATE OR REPLACE FUNCTION validate_enum_fields()
RETURNS TRIGGER AS $$
BEGIN
  -- Validate profiles.role
  IF TG_TABLE_NAME = 'profiles' AND NEW.role IS NOT NULL THEN
    IF NOT validate_enum_value('ROLE', NEW.role) THEN
      RAISE EXCEPTION 'Invalid role: %. Valid values: %', 
        NEW.role, 
        (SELECT string_agg(enum_key, ', ') FROM system_enums WHERE category_id = 'ROLE' AND is_active = true);
    END IF;
  END IF;

  -- Validate doctors.status
  IF TG_TABLE_NAME = 'doctors' AND NEW.status IS NOT NULL THEN
    IF NOT validate_enum_value('DOCTOR_STATUS', NEW.status) THEN
      RAISE EXCEPTION 'Invalid doctor status: %. Valid values: %', 
        NEW.status,
        (SELECT string_agg(enum_key, ', ') FROM system_enums WHERE category_id = 'DOCTOR_STATUS' AND is_active = true);
    END IF;
  END IF;

  -- Validate patients.gender
  IF TG_TABLE_NAME = 'patients' AND NEW.gender IS NOT NULL THEN
    IF NOT validate_enum_value('GENDER', NEW.gender) THEN
      RAISE EXCEPTION 'Invalid gender: %. Valid values: %', 
        NEW.gender,
        (SELECT string_agg(enum_key, ', ') FROM system_enums WHERE category_id = 'GENDER' AND is_active = true);
    END IF;
  END IF;

  -- Validate patients.blood_type
  IF TG_TABLE_NAME = 'patients' AND NEW.blood_type IS NOT NULL THEN
    IF NOT validate_enum_value('BLOOD_TYPE', NEW.blood_type) THEN
      RAISE EXCEPTION 'Invalid blood type: %. Valid values: %', 
        NEW.blood_type,
        (SELECT string_agg(enum_key, ', ') FROM system_enums WHERE category_id = 'BLOOD_TYPE' AND is_active = true);
    END IF;
  END IF;

  -- Validate patients.status
  IF TG_TABLE_NAME = 'patients' AND NEW.status IS NOT NULL THEN
    IF NOT validate_enum_value('PATIENT_STATUS', NEW.status) THEN
      RAISE EXCEPTION 'Invalid patient status: %. Valid values: %', 
        NEW.status,
        (SELECT string_agg(enum_key, ', ') FROM system_enums WHERE category_id = 'PATIENT_STATUS' AND is_active = true);
    END IF;
  END IF;

  -- Validate appointments.type
  IF TG_TABLE_NAME = 'appointments' AND NEW.type IS NOT NULL THEN
    IF NOT validate_enum_value('APPOINTMENT_TYPE', NEW.type) THEN
      RAISE EXCEPTION 'Invalid appointment type: %. Valid values: %', 
        NEW.type,
        (SELECT string_agg(enum_key, ', ') FROM system_enums WHERE category_id = 'APPOINTMENT_TYPE' AND is_active = true);
    END IF;
  END IF;

  -- Validate appointments.status
  IF TG_TABLE_NAME = 'appointments' AND NEW.status IS NOT NULL THEN
    IF NOT validate_enum_value('APPOINTMENT_STATUS', NEW.status) THEN
      RAISE EXCEPTION 'Invalid appointment status: %. Valid values: %', 
        NEW.status,
        (SELECT string_agg(enum_key, ', ') FROM system_enums WHERE category_id = 'APPOINTMENT_STATUS' AND is_active = true);
    END IF;
  END IF;

  -- Validate rooms.room_type
  IF TG_TABLE_NAME = 'rooms' AND NEW.room_type IS NOT NULL THEN
    IF NOT validate_enum_value('ROOM_TYPE', NEW.room_type) THEN
      RAISE EXCEPTION 'Invalid room type: %. Valid values: %', 
        NEW.room_type,
        (SELECT string_agg(enum_key, ', ') FROM system_enums WHERE category_id = 'ROOM_TYPE' AND is_active = true);
    END IF;
  END IF;

  -- Validate rooms.status
  IF TG_TABLE_NAME = 'rooms' AND NEW.status IS NOT NULL THEN
    IF NOT validate_enum_value('ROOM_STATUS', NEW.status) THEN
      RAISE EXCEPTION 'Invalid room status: %. Valid values: %', 
        NEW.status,
        (SELECT string_agg(enum_key, ', ') FROM system_enums WHERE category_id = 'ROOM_STATUS' AND is_active = true);
    END IF;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for enum validation
DROP TRIGGER IF EXISTS validate_profiles_enum ON profiles;
CREATE TRIGGER validate_profiles_enum
  BEFORE INSERT OR UPDATE ON profiles
  FOR EACH ROW EXECUTE FUNCTION validate_enum_fields();

DROP TRIGGER IF EXISTS validate_doctors_enum ON doctors;
CREATE TRIGGER validate_doctors_enum
  BEFORE INSERT OR UPDATE ON doctors
  FOR EACH ROW EXECUTE FUNCTION validate_enum_fields();

DROP TRIGGER IF EXISTS validate_patients_enum ON patients;
CREATE TRIGGER validate_patients_enum
  BEFORE INSERT OR UPDATE ON patients
  FOR EACH ROW EXECUTE FUNCTION validate_enum_fields();

DROP TRIGGER IF EXISTS validate_appointments_enum ON appointments;
CREATE TRIGGER validate_appointments_enum
  BEFORE INSERT OR UPDATE ON appointments
  FOR EACH ROW EXECUTE FUNCTION validate_enum_fields();

DROP TRIGGER IF EXISTS validate_rooms_enum ON rooms;
CREATE TRIGGER validate_rooms_enum
  BEFORE INSERT OR UPDATE ON rooms
  FOR EACH ROW EXECUTE FUNCTION validate_enum_fields();

-- =====================================================
-- 6. SUCCESS MESSAGE
-- =====================================================

SELECT 
  '✅ DATABASE FIXES COMPLETED SUCCESSFULLY!' as status,
  'All critical issues have been addressed' as message,
  'Your database is now more robust and secure' as result;
