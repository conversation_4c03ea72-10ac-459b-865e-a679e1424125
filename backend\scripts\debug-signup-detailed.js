const { createClient } = require('@supabase/supabase-js');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function debugSignupIssue() {
  console.log('🔍 DETAILED SIGNUP DEBUG ANALYSIS');
  console.log('=====================================');
  
  try {
    // 1. Check if profiles table exists and structure
    console.log('\n1️⃣ Checking profiles table structure...');
    const { data: tableInfo, error: tableError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable')
      .eq('table_name', 'profiles')
      .eq('table_schema', 'public');
    
    if (tableError) {
      console.error('❌ Error checking table structure:', tableError);
    } else {
      console.log('✅ Profiles table columns:');
      tableInfo.forEach(col => {
        console.log(`   ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
      });
    }

    // 2. Check RLS status
    console.log('\n2️⃣ Checking RLS status...');
    const { data: rlsInfo, error: rlsError } = await supabase
      .from('pg_class')
      .select('relname, relrowsecurity')
      .eq('relname', 'profiles');
    
    if (rlsError) {
      console.error('❌ Error checking RLS:', rlsError);
    } else {
      console.log('✅ RLS status:', rlsInfo[0]?.relrowsecurity ? 'ENABLED' : 'DISABLED');
    }

    // 3. Check current policies using a different approach
    console.log('\n3️⃣ Checking current policies...');
    const { data: policies, error: policiesError } = await supabase
      .rpc('get_policies_for_table', { table_name: 'profiles' })
      .catch(async () => {
        // Fallback: try direct query
        return await supabase
          .from('information_schema.table_privileges')
          .select('*')
          .eq('table_name', 'profiles');
      });
    
    if (policiesError) {
      console.log('⚠️ Could not check policies directly');
    } else {
      console.log('📋 Found policies/privileges:', policies?.length || 0);
    }

    // 4. Check if trigger function exists
    console.log('\n4️⃣ Checking trigger function...');
    const { data: functions, error: funcError } = await supabase
      .from('information_schema.routines')
      .select('routine_name, routine_type')
      .eq('routine_name', 'handle_new_user')
      .eq('routine_schema', 'public');
    
    if (funcError) {
      console.error('❌ Error checking function:', funcError);
    } else if (functions.length === 0) {
      console.error('❌ handle_new_user function NOT FOUND');
    } else {
      console.log('✅ handle_new_user function exists');
    }

    // 5. Check if trigger exists
    console.log('\n5️⃣ Checking trigger...');
    const { data: triggers, error: triggerError } = await supabase
      .from('information_schema.triggers')
      .select('trigger_name, event_manipulation, action_timing')
      .eq('trigger_name', 'on_auth_user_created');
    
    if (triggerError) {
      console.error('❌ Error checking trigger:', triggerError);
    } else if (triggers.length === 0) {
      console.error('❌ on_auth_user_created trigger NOT FOUND');
    } else {
      console.log('✅ on_auth_user_created trigger exists');
      console.log(`   Event: ${triggers[0].event_manipulation}, Timing: ${triggers[0].action_timing}`);
    }

    // 6. Test direct INSERT to profiles (bypass auth)
    console.log('\n6️⃣ Testing direct INSERT to profiles...');
    const testId = crypto.randomUUID();
    const { data: insertData, error: insertError } = await supabase
      .from('profiles')
      .insert({
        id: testId,
        email: '<EMAIL>',
        full_name: 'Test User',
        role: 'patient',
        is_active: true
      })
      .select();
    
    if (insertError) {
      console.error('❌ Direct INSERT failed:', insertError);
      console.error('   This confirms RLS is blocking INSERTs');
    } else {
      console.log('✅ Direct INSERT successful');
      // Clean up
      await supabase.from('profiles').delete().eq('id', testId);
    }

    // 7. Check auth.users table access
    console.log('\n7️⃣ Checking auth.users access...');
    const { data: authUsers, error: authError } = await supabase
      .from('auth.users')
      .select('id')
      .limit(1);
    
    if (authError) {
      console.error('❌ Cannot access auth.users:', authError);
    } else {
      console.log('✅ Can access auth.users table');
    }

    console.log('\n🎯 DIAGNOSIS COMPLETE');
    console.log('=====================================');
    
  } catch (error) {
    console.error('❌ Unexpected error during debug:', error);
  }
}

debugSignupIssue();
