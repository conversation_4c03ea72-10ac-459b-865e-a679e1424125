-- =====================================================
-- FIX MISSING COLUMNS SCRIPT
-- =====================================================
-- This script adds missing columns to existing tables
-- Run this FIRST before other fix scripts

-- =====================================================
-- 1. ADD MISSING LOCATION COLUMN TO DEPARTMENTS
-- =====================================================

-- Add location column to departments if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'departments' 
      AND column_name = 'location' 
      AND table_schema = 'public'
  ) THEN
    ALTER TABLE departments ADD COLUMN location TEXT;
  END IF;
END $$;

-- =====================================================
-- 2. UPDATE EXISTING DEPARTMENTS WITH SAMPLE LOCATIONS
-- =====================================================

-- Update existing departments with sample locations
UPDATE departments SET location = 'Tầng 1, Tòa A' WHERE department_id = 'DEPT001' AND location IS NULL;
UPDATE departments SET location = 'Tầng 2, Tòa A' WHERE department_id = 'DEPT002' AND location IS NULL;
UPDATE departments SET location = 'Tầng 3, Tòa A' WHERE department_id = 'DEPT003' AND location IS NULL;
UPDATE departments SET location = 'Tầng 1, Tòa B' WHERE department_id = 'DEPT004' AND location IS NULL;
UPDATE departments SET location = 'Tầng 2, Tòa B' WHERE department_id = 'DEPT005' AND location IS NULL;

-- =====================================================
-- 3. VERIFY COLUMN EXISTS
-- =====================================================

-- Check if location column now exists
SELECT 
  'Column Check' as test_type,
  table_name,
  column_name,
  data_type,
  '✅ EXISTS' as status
FROM information_schema.columns 
WHERE table_name = 'departments' 
  AND column_name = 'location' 
  AND table_schema = 'public';

-- =====================================================
-- 4. SUCCESS MESSAGE
-- =====================================================

SELECT 
  '✅ MISSING COLUMNS FIXED!' as status,
  'Location column added to departments table' as message,
  'You can now run the other fix scripts' as next_step;
