-- =====================================================
-- IMPROVE RLS POLICIES AND CREATE VIEWS
-- =====================================================
-- This script improves RLS policies and creates useful views
-- Run this AFTER fix-database-issues-supabase.sql

-- =====================================================
-- 0. FIX MISSING COLUMNS
-- =====================================================

-- Add location column to departments if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'departments'
      AND column_name = 'location'
      AND table_schema = 'public'
  ) THEN
    ALTER TABLE departments ADD COLUMN location TEXT;
    RAISE NOTICE 'Added location column to departments table';
  END IF;
END $$;

-- =====================================================
-- 1. IMPROVE RLS POLICIES
-- =====================================================

-- Add missing INSERT policies for patients
DROP POLICY IF EXISTS "Patients can create own profile" ON patients;
CREATE POLICY "Patients can create own profile" ON patients
  FOR INSERT WITH CHECK (profile_id = auth.uid());

-- Add missing INSERT policies for doctors
DROP POLICY IF EXISTS "Allow doctor profile creation" ON doctors;
CREATE POLICY "Allow doctor profile creation" ON doctors
  FOR INSERT WITH CHECK (true); -- Admin will handle this via application logic

-- Improve appointments policies
DROP POLICY IF EXISTS "Patients can create appointments" ON appointments;
CREATE POLICY "Patients can create appointments" ON appointments
  FOR INSERT WITH CHECK (
    patient_id IN (
      SELECT patient_id FROM patients WHERE profile_id = auth.uid()
    )
  );

DROP POLICY IF EXISTS "Doctors can create appointments" ON appointments;
CREATE POLICY "Doctors can create appointments" ON appointments
  FOR INSERT WITH CHECK (
    doctor_id IN (
      SELECT doctor_id FROM doctors WHERE profile_id = auth.uid()
    )
  );

-- Add UPDATE policies for appointments
DROP POLICY IF EXISTS "Patients can update own appointments" ON appointments;
CREATE POLICY "Patients can update own appointments" ON appointments
  FOR UPDATE USING (
    patient_id IN (
      SELECT patient_id FROM patients WHERE profile_id = auth.uid()
    )
  );

DROP POLICY IF EXISTS "Doctors can update their appointments" ON appointments;
CREATE POLICY "Doctors can update their appointments" ON appointments
  FOR UPDATE USING (
    doctor_id IN (
      SELECT doctor_id FROM doctors WHERE profile_id = auth.uid()
    )
  );

-- =====================================================
-- 2. CREATE VIEWS FOR BETTER DATA ACCESS
-- =====================================================

-- View for complete doctor information
CREATE OR REPLACE VIEW v_doctors_complete AS
SELECT
  d.*,
  p.full_name,
  p.email,
  p.phone_number,
  p.avatar_url,
  p.is_active as profile_active,
  dept.name as department_name,
  dept.location as department_location
FROM doctors d
JOIN profiles p ON d.profile_id = p.id
LEFT JOIN departments dept ON d.department_id = dept.department_id;

-- View for complete patient information
CREATE OR REPLACE VIEW v_patients_complete AS
SELECT
  pt.*,
  p.full_name,
  p.email,
  p.phone_number,
  p.avatar_url,
  p.is_active as profile_active
FROM patients pt
JOIN profiles p ON pt.profile_id = p.id;

-- View for appointment details
CREATE OR REPLACE VIEW v_appointments_detailed AS
SELECT
  a.*,
  p_patient.full_name as patient_name,
  p_patient.phone_number as patient_phone,
  p_doctor.full_name as doctor_name,
  d.specialization as doctor_specialization,
  dept.name as department_name,
  r.room_number,
  r.location as room_location
FROM appointments a
JOIN patients pt ON a.patient_id = pt.patient_id
JOIN profiles p_patient ON pt.profile_id = p_patient.id
JOIN doctors d ON a.doctor_id = d.doctor_id
JOIN profiles p_doctor ON d.profile_id = p_doctor.id
LEFT JOIN departments dept ON d.department_id = dept.department_id
LEFT JOIN rooms r ON a.room_id = r.room_id;

-- View for enum options (useful for frontend)
CREATE OR REPLACE VIEW v_enum_options AS
SELECT
  se.category_id,
  ec.display_name_vi as category_name,
  se.enum_key,
  se.display_name_vi,
  se.display_name_en,
  se.description_vi,
  se.description_en,
  se.sort_order,
  se.color_code,
  se.icon_name,
  se.is_default
FROM system_enums se
JOIN enum_categories ec ON se.category_id = ec.category_id
WHERE se.is_active = true AND ec.is_active = true
ORDER BY se.category_id, se.sort_order, se.display_name_vi;

-- =====================================================
-- 3. CREATE USEFUL FUNCTIONS
-- =====================================================

-- Function to get available time slots for a doctor
CREATE OR REPLACE FUNCTION get_available_slots(
  p_doctor_id TEXT,
  p_date DATE,
  p_duration_minutes INTEGER DEFAULT 30
)
RETURNS TABLE(
  slot_time TIMESTAMPTZ,
  is_available BOOLEAN
) AS $$
DECLARE
  start_hour INTEGER := 8;  -- 8 AM
  end_hour INTEGER := 17;   -- 5 PM
  slot_start TIMESTAMPTZ;
  slot_end TIMESTAMPTZ;
BEGIN
  FOR hour_offset IN 0..(end_hour - start_hour) LOOP
    FOR minute_offset IN 0..1 LOOP -- 30-minute slots
      slot_start := p_date + (start_hour + hour_offset || ' hours')::INTERVAL + (minute_offset * 30 || ' minutes')::INTERVAL;
      slot_end := slot_start + (p_duration_minutes || ' minutes')::INTERVAL;

      -- Check if slot is available
      slot_time := slot_start;
      is_available := NOT EXISTS (
        SELECT 1 FROM appointments
        WHERE doctor_id = p_doctor_id
          AND appointment_datetime < slot_end
          AND appointment_datetime + (duration_minutes || ' minutes')::INTERVAL > slot_start
          AND status NOT IN ('cancelled', 'completed')
      );

      RETURN NEXT;
    END LOOP;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to get doctor statistics
CREATE OR REPLACE FUNCTION get_doctor_stats(p_doctor_id TEXT)
RETURNS TABLE(
  total_appointments INTEGER,
  completed_appointments INTEGER,
  cancelled_appointments INTEGER,
  average_rating DECIMAL,
  total_patients INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    COUNT(*)::INTEGER as total_appointments,
    COUNT(CASE WHEN status = 'completed' THEN 1 END)::INTEGER as completed_appointments,
    COUNT(CASE WHEN status = 'cancelled' THEN 1 END)::INTEGER as cancelled_appointments,
    0.0::DECIMAL as average_rating, -- Placeholder for future rating system
    COUNT(DISTINCT patient_id)::INTEGER as total_patients
  FROM appointments
  WHERE doctor_id = p_doctor_id;
END;
$$ LANGUAGE plpgsql;

-- Function to check appointment conflicts
CREATE OR REPLACE FUNCTION check_appointment_conflict(
  p_doctor_id TEXT,
  p_appointment_datetime TIMESTAMPTZ,
  p_duration_minutes INTEGER DEFAULT 30,
  p_exclude_appointment_id TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
  conflict_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO conflict_count
  FROM appointments
  WHERE doctor_id = p_doctor_id
    AND appointment_datetime < p_appointment_datetime + (p_duration_minutes || ' minutes')::INTERVAL
    AND appointment_datetime + (duration_minutes || ' minutes')::INTERVAL > p_appointment_datetime
    AND status NOT IN ('cancelled', 'completed')
    AND (p_exclude_appointment_id IS NULL OR appointment_id != p_exclude_appointment_id);

  RETURN conflict_count > 0;
END;
$$ LANGUAGE plpgsql;

-- Function to get default enum value
CREATE OR REPLACE FUNCTION get_default_enum_value(p_category_id TEXT)
RETURNS TEXT AS $$
DECLARE
  default_value TEXT;
BEGIN
  SELECT enum_key INTO default_value
  FROM system_enums
  WHERE category_id = p_category_id
    AND is_default = true
    AND is_active = true
  LIMIT 1;

  -- If no default found, return first active enum
  IF default_value IS NULL THEN
    SELECT enum_key INTO default_value
    FROM system_enums
    WHERE category_id = p_category_id
      AND is_active = true
    ORDER BY sort_order, enum_key
    LIMIT 1;
  END IF;

  RETURN default_value;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 4. CREATE TRIGGERS FOR BUSINESS LOGIC
-- =====================================================

-- Function to prevent appointment conflicts
CREATE OR REPLACE FUNCTION prevent_appointment_conflicts()
RETURNS TRIGGER AS $$
BEGIN
  -- Check for doctor conflicts
  IF check_appointment_conflict(
    NEW.doctor_id,
    NEW.appointment_datetime,
    NEW.duration_minutes,
    CASE WHEN TG_OP = 'UPDATE' THEN OLD.appointment_id ELSE NULL END
  ) THEN
    RAISE EXCEPTION 'Doctor % already has an appointment at this time', NEW.doctor_id;
  END IF;

  -- Check for room conflicts (if room is specified)
  IF NEW.room_id IS NOT NULL THEN
    IF EXISTS (
      SELECT 1 FROM appointments
      WHERE room_id = NEW.room_id
        AND appointment_datetime < NEW.appointment_datetime + (NEW.duration_minutes || ' minutes')::INTERVAL
        AND appointment_datetime + (duration_minutes || ' minutes')::INTERVAL > NEW.appointment_datetime
        AND status NOT IN ('cancelled', 'completed')
        AND (TG_OP = 'INSERT' OR appointment_id != OLD.appointment_id)
    ) THEN
      RAISE EXCEPTION 'Room % is already booked at this time', NEW.room_id;
    END IF;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for appointment conflicts
DROP TRIGGER IF EXISTS prevent_appointment_conflicts_trigger ON appointments;
CREATE TRIGGER prevent_appointment_conflicts_trigger
  BEFORE INSERT OR UPDATE ON appointments
  FOR EACH ROW EXECUTE FUNCTION prevent_appointment_conflicts();

-- =====================================================
-- 5. SUCCESS MESSAGE
-- =====================================================

SELECT
  '✅ RLS POLICIES AND VIEWS CREATED SUCCESSFULLY!' as status,
  'Enhanced security and data access capabilities' as message,
  'Database optimization completed' as result;
