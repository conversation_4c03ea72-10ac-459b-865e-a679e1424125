const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

async function simpleCheck() {
  console.log('🔍 Simple Database Check');
  console.log('========================');
  
  try {
    const supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );
    
    console.log('✅ Supabase client created');
    
    // Test basic connection
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);
    
    if (error) {
      console.log('❌ Connection error:', error.message);
    } else {
      console.log('✅ Connection successful');
    }
    
    // Check tables
    const tables = ['profiles', 'doctors', 'patients', 'departments', 'appointments'];
    
    for (const table of tables) {
      try {
        const { count, error } = await supabase
          .from(table)
          .select('*', { count: 'exact', head: true });
        
        if (error) {
          console.log(`❌ ${table}: ${error.message}`);
        } else {
          console.log(`✅ ${table}: ${count || 0} records`);
        }
      } catch (err) {
        console.log(`❌ ${table}: ${err.message}`);
      }
    }
    
  } catch (err) {
    console.log('❌ Script error:', err.message);
  }
}

simpleCheck();
